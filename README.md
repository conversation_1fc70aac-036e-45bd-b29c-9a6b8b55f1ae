# SaySomething - Mental Health Platform

SaySomething is a centralized, student-friendly website designed to raise mental health awareness, guide students to relevant support systems, and reduce stigma on university campuses. The platform is informative, accessible, mobile-optimized, and includes a dedicated space for handling sexual harassment concerns.

## Project Overview

This website serves as a comprehensive resource for university students (primarily from Takoradi Technical University, Ghana) seeking mental health support and information. It provides a user-friendly interface to access various resources, awareness materials, reporting mechanisms, and self-care tools.

## Features

- **Responsive Design**: Mobile-first approach ensuring accessibility across all devices
- **Modern UI**: Clean, intuitive interface with subtle animations and transitions
- **Resource Directory**: Comprehensive catalog of campus and external mental health resources
- **Awareness Hub**: Educational content to reduce stigma around mental health
- **Report & Support**: Dedicated section for sexual harassment concerns
- **Self-Care Tools**: Practical resources for maintaining mental wellbeing

## Technology Stack

- HTML5
- CSS3 with Tailwind CSS
- Vanilla JavaScript
- Responsive design principles
- Accessibility features

## Project Structure

```
saySomething/
├── css/
│   └── styles.css
├── js/
│   └── main.js
├── images/
├── index.html
└── README.md
```

## Getting Started

1. Clone this repository
2. Open `index.html` in your browser
3. No build process or dependencies required

## Browser Compatibility

The website is designed to work on all modern browsers including:
- Chrome
- Firefox
- Safari
- Edge

## Accessibility

This project follows WCAG 2.1 guidelines to ensure accessibility for all users, including:
- Semantic HTML
- Keyboard navigation
- Screen reader compatibility
- Sufficient color contrast

## Future Enhancements

- User authentication for personalized experiences
- Interactive self-assessment tools
- Community forums
- Integration with campus systems

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Takoradi Technical University for the initiative
- Mental health professionals who provided guidance
- Student contributors and testers 