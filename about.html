<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>About Us - SaySomething</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#3B82F6",
              secondary: "#10B981",
              accent: "#F59E0B",
              light: "#F3F4F6",
              dark: "#1F2937",
            },
            fontFamily: {
              sans: ["Poppins", "sans-serif"],
              body: ["Lato", "sans-serif"],
            },
            screens: {
              sm: "640px",
              md: "901px",
              lg: "1024px",
              xl: "1280px",
              "2xl": "1536px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="css/styles.css" />
    <style type="text/tailwindcss">
      @layer utilities {
        .text-shadow {
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .transition-smooth {
          transition: all 0.3s ease-in-out;
        }
      }
      body.menu-open {
        overflow: hidden;
      }
    </style>
  </head>
  <body class="font-body bg-light text-dark page-transition" style="opacity: 0">
    <!-- Header/Navigation -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <div class="flex-shrink-0 flex items-center">
            <a href="/" class="flex items-center">
              <span class="font-sans font-bold text-2xl text-primary"
                >Say<span class="text-secondary">Something</span></span
              >
            </a>
          </div>

          <!-- Desktop Navigation -->
          <nav class="hidden md:flex space-x-8">
            <a
              href="index.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Home</a
            >
            <a
              href="resources.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Resources</a
            >
            <a
              href="awareness.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Awareness</a
            >
            <a
              href="report.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Report & Support</a
            >
            <a
              href="self-care.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Self-Care</a
            >
            <a
              href="about.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >About</a
            >
            <a
              href="contact.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Contact</a
            >
          </nav>

          <!-- Mobile menu button -->
          <div class="flex md:hidden">
            <button
              class="hamburger"
              aria-label="Toggle mobile menu"
              aria-expanded="false"
              aria-controls="mobile-menu"
            >
              <span></span>
              <span></span>
              <span></span>
              <span></span>
            </button>
          </div>
        </div>
      </div>

      <!-- Mobile menu container -->
      <div class="mobile-menu-container">
        <div class="flex flex-col items-center justify-center h-full">
          <a
            href="index.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Home</a
          >
          <a
            href="resources.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Resources</a
          >
          <a
            href="awareness.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Awareness</a
          >
          <a
            href="report.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Report & Support</a
          >
          <a
            href="self-care.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Self-Care</a
          >
          <a
            href="about.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >About</a
          >
          <a
            href="contact.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary transition-smooth"
            >Contact</a
          >

          <div class="absolute bottom-10 flex space-x-4">
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                />
              </svg>
            </a>
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                />
              </svg>
            </a>
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </header>

    <main>
      <!-- Hero Section -->
      <section
        class="relative bg-gradient-to-br from-primary/10 to-accent/10 overflow-hidden"
      >
        <!-- Background pattern -->
        <div class="absolute inset-0 opacity-10">
          <svg
            class="w-full h-full"
            viewBox="0 0 100 100"
            preserveAspectRatio="none"
          >
            <defs>
              <pattern
                id="grid"
                width="8"
                height="8"
                patternUnits="userSpaceOnUse"
              >
                <path
                  d="M0 8L8 0"
                  stroke="currentColor"
                  stroke-width="0.5"
                  fill="none"
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
          </svg>
        </div>

        <div
          class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32 relative"
        >
          <div class="grid md:grid-cols-2 gap-12 items-center">
            <!-- Left content -->
            <div class="space-y-8 animate-fade-in">
              <div class="space-y-4">
                <h1
                  class="font-sans font-bold text-4xl md:text-5xl lg:text-6xl text-dark text-shadow leading-tight"
                >
                  About <span class="text-primary">SaySomething</span>
                </h1>
                <p class="text-lg md:text-xl text-gray-600 max-w-lg">
                  Learn about our mission, our team, and the impact we're making
                  to support student mental health at Takoradi Technical
                  University.
                </p>
              </div>

              <div class="flex flex-wrap gap-4">
                <a
                  href="#our-story"
                  class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-smooth hover-lift"
                >
                  Our Story
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 ml-2"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </a>
                <a
                  href="#team"
                  class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent transition-smooth hover-lift"
                >
                  Meet Our Team
                </a>
              </div>
            </div>

            <!-- Right content - Illustration -->
            <div
              class="relative hidden md:block animate-fade-in"
              style="animation-delay: 0.3s"
            >
              <div
                class="absolute -top-6 -right-6 w-24 h-24 bg-accent/20 rounded-full blur-2xl"
              ></div>
              <div
                class="absolute -bottom-8 -left-8 w-32 h-32 bg-primary/20 rounded-full blur-2xl"
              ></div>

              <div
                class="relative bg-white rounded-2xl shadow-xl overflow-hidden p-1 img-hover-zoom"
              >
                <div class="aspect-w-4 aspect-h-3 rounded-xl overflow-hidden">
                  <img
                    src="https://images.unsplash.com/photo-1523240795612-9a054b0db644?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                    alt="Diverse group of students collaborating"
                    class="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Our Story Section -->
      <section id="our-story" class="py-16 md:py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16 animate-fade-in">
            <h2 class="text-3xl md:text-4xl font-sans font-bold text-dark">
              Our Story
            </h2>
            <div class="w-24 h-1 bg-primary mx-auto mt-4 rounded-full"></div>
          </div>

          <div
            class="grid md:grid-cols-2 gap-12 lg:gap-16 items-center stagger-children"
          >
            <!-- Left side - Image -->
            <div class="relative">
              <div
                class="absolute -top-6 -left-6 w-24 h-24 bg-primary/20 rounded-full blur-2xl"
              ></div>
              <div
                class="absolute -bottom-8 -right-8 w-32 h-32 bg-secondary/20 rounded-full blur-2xl"
              ></div>

              <div
                class="relative bg-white rounded-2xl shadow-xl overflow-hidden p-1 img-hover-zoom"
              >
                <div
                  class="aspect-w-4 aspect-h-3 rounded-xl overflow-hidden"
                  style="height: 25rem"
                >
                  <img
                    src="./images/ttu.png"
                    alt="Students collaborating on campus"
                    class="w-full h-full object-contain"
                  />
                </div>
              </div>
            </div>

            <!-- Right side - Content -->
            <div class="space-y-6">
              <h3 class="text-2xl font-sans font-semibold text-dark">
                How SaySomething Began
              </h3>
              <p class="text-gray-600">
                SaySomething was born from a simple observation: many students
                at Takoradi Technical University were struggling with mental
                health challenges, but didn't know where to turn for help.
              </p>
              <p class="text-gray-600">
                In 2023, a group of concerned students and faculty members came
                together to address this gap. They recognized that while
                resources existed, they were fragmented and often difficult to
                navigate, especially for students in distress.
              </p>
              <p class="text-gray-600">
                The team conducted surveys and focus groups across campus to
                understand the specific needs of TTU students. The findings were
                clear: students needed a centralized, accessible platform that
                could guide them to appropriate resources and help destigmatize
                conversations around mental health.
              </p>
              <p class="text-gray-600">
                With support from the Student Union, Counseling Center, and
                Department of Information Technology, SaySomething was developed
                as a comprehensive digital resource hub - a place where any
                student could find the support they need, when they need it
                most.
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- Our Mission & Values Section -->
      <section class="py-16 md:py-24 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16 animate-fade-in">
            <h2 class="text-3xl md:text-4xl font-sans font-bold text-dark">
              Mission & Values
            </h2>
            <div class="w-24 h-1 bg-secondary mx-auto mt-4 rounded-full"></div>
          </div>

          <div class="grid md:grid-cols-2 gap-12 lg:gap-16 items-start">
            <!-- Mission -->
            <div class="bg-white rounded-xl p-8 shadow-sm">
              <div
                class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-6"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-8 w-8 text-primary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
              </div>
              <h3 class="text-xl font-sans font-semibold mb-4">Our Mission</h3>
              <p class="text-gray-600 mb-4">
                SaySomething exists to transform how university students access
                mental health support. We're building a platform that breaks
                down barriers, connects students to resources, and fosters a
                campus culture where mental health is prioritized.
              </p>
              <p class="text-gray-600">
                We believe that every student deserves access to mental health
                support that is:
              </p>
              <ul class="mt-4 space-y-2">
                <li class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-primary mr-2 mt-0.5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  <span class="text-gray-600"
                    ><strong class="text-gray-700">Accessible</strong> -
                    Available to all students regardless of background</span
                  >
                </li>
                <li class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-primary mr-2 mt-0.5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  <span class="text-gray-600"
                    ><strong class="text-gray-700">Comprehensive</strong> -
                    Addressing the full spectrum of mental health needs</span
                  >
                </li>
                <li class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-primary mr-2 mt-0.5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  <span class="text-gray-600"
                    ><strong class="text-gray-700">Destigmatizing</strong> -
                    Normalizing conversations about mental health</span
                  >
                </li>
                <li class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-primary mr-2 mt-0.5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  <span class="text-gray-600"
                    ><strong class="text-gray-700">Empowering</strong> -
                    Equipping students with knowledge and tools</span
                  >
                </li>
              </ul>
            </div>

            <!-- Values -->
            <div class="bg-white rounded-xl p-8 shadow-sm">
              <div
                class="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center mb-6"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-8 w-8 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                  />
                </svg>
              </div>
              <h3 class="text-xl font-sans font-semibold mb-4">Our Values</h3>
              <div class="space-y-4">
                <div class="pb-4 border-b border-gray-100">
                  <h4 class="font-medium text-gray-800 mb-2">Compassion</h4>
                  <p class="text-gray-600">
                    We approach all our work with empathy and understanding,
                    recognizing that every student's experience is unique and
                    valid.
                  </p>
                </div>
                <div class="pb-4 border-b border-gray-100">
                  <h4 class="font-medium text-gray-800 mb-2">Inclusivity</h4>
                  <p class="text-gray-600">
                    We are committed to creating resources that serve all
                    students, regardless of background, identity, or specific
                    needs.
                  </p>
                </div>
                <div class="pb-4 border-b border-gray-100">
                  <h4 class="font-medium text-gray-800 mb-2">Evidence-Based</h4>
                  <p class="text-gray-600">
                    Our resources and recommendations are grounded in research
                    and best practices in mental health and student wellness.
                  </p>
                </div>
                <div>
                  <h4 class="font-medium text-gray-800 mb-2">
                    Student-Centered
                  </h4>
                  <p class="text-gray-600">
                    We design everything with students' needs at the forefront,
                    continuously seeking feedback to improve our platform.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Team Section -->
      <section id="team" class="py-16 md:py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16 animate-fade-in">
            <h2 class="text-3xl md:text-4xl font-sans font-bold text-dark">
              Meet Our Team
            </h2>
            <div class="w-24 h-1 bg-accent mx-auto mt-4 rounded-full"></div>
            <p class="mt-6 text-lg text-gray-600 max-w-3xl mx-auto">
              SaySomething is made possible by a dedicated team of students,
              faculty, and staff who are passionate about mental health support.
            </p>
          </div>

          <div
            class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8 stagger-children"
          >
            <!-- Team Member 1 -->
            <div
              class="bg-light rounded-xl p-6 text-center hover:shadow-md transition-smooth"
            >
              <div class="relative w-32 h-32 mx-auto mb-4">
                <div
                  class="absolute inset-0 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20"
                ></div>
                <img
                  src=""
                  alt="Ransford Quayson"
                  class="rounded-full w-full h-full object-cover border-4 border-white"
                />
              </div>
              <h3 class="text-lg font-sans font-semibold">Ransford Quayson</h3>
              <p class="text-gray-500 text-sm mb-3">Project Lead</p>
              <p class="text-gray-600 text-sm">
                Information Technology student with a passion for creating
                digital solutions that make a difference.
              </p>
            </div>

            <!-- Team Member 2 -->
            <div
              class="bg-light rounded-xl p-6 text-center hover:shadow-md transition-smooth"
            >
              <div class="relative w-32 h-32 mx-auto mb-4">
                <div
                  class="absolute inset-0 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20"
                ></div>
                <img
                  src=""
                  alt="Abena Mensah"
                  class="rounded-full w-full h-full object-cover border-4 border-white"
                />
              </div>
              <h3 class="text-lg font-sans font-semibold">Abena Mensah</h3>
              <p class="text-gray-500 text-sm mb-3">Content Specialist</p>
              <p class="text-gray-600 text-sm">
                Psychology student focused on creating accessible mental health
                resources.
              </p>
            </div>

            <!-- Team Member 3 -->
            <div
              class="bg-light rounded-xl p-6 text-center hover:shadow-md transition-smooth"
            >
              <div class="relative w-32 h-32 mx-auto mb-4">
                <div
                  class="absolute inset-0 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20"
                ></div>
                <img
                  src=""
                  alt="Kwame Osei"
                  class="rounded-full w-full h-full object-cover border-4 border-white"
                />
              </div>
              <h3 class="text-lg font-sans font-semibold">Kwame Osei</h3>
              <p class="text-gray-500 text-sm mb-3">Outreach Coordinator</p>
              <p class="text-gray-600 text-sm">
                Social Work student dedicated to connecting students with mental
                health resources.
              </p>
            </div>

            <!-- Team Member 4 -->
            <div
              class="bg-light rounded-xl p-6 text-center hover:shadow-md transition-smooth"
            >
              <div class="relative w-32 h-32 mx-auto mb-4">
                <div
                  class="absolute inset-0 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20"
                ></div>
                <img
                  src=""
                  alt="Dr. Akosua Boateng"
                  class="rounded-full w-full h-full object-cover border-4 border-white"
                />
              </div>
              <h3 class="text-lg font-sans font-semibold">
                Dr. Akosua Boateng
              </h3>
              <p class="text-gray-500 text-sm mb-3">Faculty Advisor</p>
              <p class="text-gray-600 text-sm">
                Professor of Psychology providing expert guidance on mental
                health resources.
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- Partners Section -->
      <section class="py-16 md:py-24 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16 animate-fade-in">
            <h2 class="text-3xl md:text-4xl font-sans font-bold text-dark">
              Our Partners
            </h2>
            <div class="w-24 h-1 bg-primary mx-auto mt-4 rounded-full"></div>
            <p class="mt-6 text-lg text-gray-600 max-w-3xl mx-auto">
              SaySomething is made possible through collaboration with these
              campus and community organizations.
            </p>
          </div>

          <div
            class="grid grid-cols-2 md:grid-cols-3 gap-8 md:gap-12 stagger-children"
          >
            <!-- Partner 1 -->
            <div
              class="bg-white rounded-xl p-6 text-center hover:shadow-md transition-smooth flex flex-col items-center"
            >
              <div
                class="w-24 h-24 bg-primary/10 rounded-full flex items-center justify-center mb-4"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-12 w-12 text-primary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                  />
                </svg>
              </div>
              <h3 class="text-lg font-sans font-semibold">
                TTU Counseling Center
              </h3>
              <p class="text-gray-600 mt-2">
                Provides professional mental health services to students,
                including individual counseling, group therapy, and crisis
                intervention.
              </p>
            </div>

            <!-- Partner 2 -->
            <div
              class="bg-white rounded-xl p-6 text-center hover:shadow-md transition-smooth flex flex-col items-center"
            >
              <div
                class="w-24 h-24 bg-secondary/10 rounded-full flex items-center justify-center mb-4"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-12 w-12 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                  />
                </svg>
              </div>
              <h3 class="text-lg font-sans font-semibold">TTU Student Union</h3>
              <p class="text-gray-600 mt-2">
                Advocates for student needs and helps connect SaySomething with
                the broader student community through events and outreach.
              </p>
            </div>

            <!-- Partner 3 -->
            <div
              class="bg-white rounded-xl p-6 text-center hover:shadow-md transition-smooth flex flex-col items-center"
            >
              <div
                class="w-24 h-24 bg-accent/10 rounded-full flex items-center justify-center mb-4"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-12 w-12 text-accent"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"
                  />
                </svg>
              </div>
              <h3 class="text-lg font-sans font-semibold">Health Services</h3>
              <p class="text-gray-600 mt-2">
                Provides medical support and works closely with the Counseling
                Center to offer comprehensive care for students' physical and
                mental health.
              </p>
            </div>

            <!-- Partner 4 -->
            <div
              class="bg-white rounded-xl p-6 text-center hover:shadow-md transition-smooth flex flex-col items-center"
            >
              <div
                class="w-24 h-24 bg-primary/10 rounded-full flex items-center justify-center mb-4"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-12 w-12 text-primary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                  />
                </svg>
              </div>
              <h3 class="text-lg font-sans font-semibold">Campus Security</h3>
              <p class="text-gray-600 mt-2">
                Works with SaySomething to ensure prompt response to crisis
                situations and maintain a safe campus environment for all
                students.
              </p>
            </div>

            <!-- Partner 5 -->
            <div
              class="bg-white rounded-xl p-6 text-center hover:shadow-md transition-smooth flex flex-col items-center"
            >
              <div
                class="w-24 h-24 bg-secondary/10 rounded-full flex items-center justify-center mb-4"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-12 w-12 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
              </div>
              <h3 class="text-lg font-sans font-semibold">
                Department of Psychology
              </h3>
              <p class="text-gray-600 mt-2">
                Provides expert guidance on mental health content and connects
                students with research-based resources and learning
                opportunities.
              </p>
            </div>

            <!-- Partner 6 -->
            <div
              class="bg-white rounded-xl p-6 text-center hover:shadow-md transition-smooth flex flex-col items-center"
            >
              <div
                class="w-24 h-24 bg-accent/10 rounded-full flex items-center justify-center mb-4"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-12 w-12 text-accent"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
                  />
                </svg>
              </div>
              <h3 class="text-lg font-sans font-semibold">
                Mental Health Ghana
              </h3>
              <p class="text-gray-600 mt-2">
                A national organization that provides additional resources,
                training, and support for campus mental health initiatives.
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- Contact CTA Section -->
      <section class="py-16 md:py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div
            class="bg-gradient-to-r from-primary/10 to-accent/10 rounded-2xl p-8 md:p-10"
          >
            <div
              class="flex flex-col md:flex-row items-center justify-between gap-8"
            >
              <div class="max-w-2xl">
                <h2
                  class="text-2xl md:text-3xl font-sans font-bold text-dark mb-4"
                >
                  Want to Get Involved?
                </h2>
                <p class="text-gray-700">
                  We're always looking for passionate students, faculty, and
                  staff to join our mission. Whether you want to contribute
                  content, help with outreach, or provide feedback, we'd love to
                  hear from you.
                </p>
              </div>

              <div class="flex flex-col sm:flex-row gap-4">
                <a
                  href="contact.html"
                  class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-smooth hover-lift"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  Contact Us
                </a>
                <a
                  href="#"
                  class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent transition-smooth hover-lift"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                  Upcoming Events
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid md:grid-cols-4 gap-8">
          <!-- Logo and description -->
          <div class="col-span-1 md:col-span-2">
            <a href="/" class="flex items-center">
              <span class="font-sans font-bold text-2xl text-white"
                >Say<span class="text-secondary">Something</span></span
              >
            </a>
            <p class="mt-4 text-gray-300">
              A centralized, student-friendly platform designed to raise mental
              health awareness, guide students to relevant support systems, and
              reduce stigma on university campuses.
            </p>
          </div>

          <!-- Quick links -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="index.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Home</a
                >
              </li>
              <li>
                <a
                  href="resources.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Resources</a
                >
              </li>
              <li>
                <a
                  href="awareness.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Awareness</a
                >
              </li>
              <li>
                <a
                  href="report.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Report & Support</a
                >
              </li>
              <li>
                <a
                  href="self-care.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Self-Care</a
                >
              </li>
            </ul>
          </div>

          <!-- Contact -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Contact</h3>
            <ul class="space-y-2">
              <li class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
                <a
                  href="mailto:<EMAIL>"
                  class="text-gray-300 hover:text-white transition-smooth"
                  ><EMAIL></a
                >
              </li>
              <li class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
                <span class="text-gray-300"
                  >Takoradi Technical University, Ghana</span
                >
              </li>
            </ul>

            <div class="mt-4 flex space-x-4">
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Facebook</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                    clip-rule="evenodd"
                  />
                </svg>
              </a>
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Instagram</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                    clip-rule="evenodd"
                  />
                </svg>
              </a>
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Twitter</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                  />
                </svg>
              </a>
            </div>
          </div>
        </div>

        <div
          class="mt-12 pt-8 border-t border-gray-800 text-center text-gray-400 text-sm"
        >
          <p>Ransford Quayson | BT/ITW/24/009</p>
          <p>&copy; 2025 SaySomething. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <!-- Emergency Call Button -->
    <a
      href="tel:+233500000000"
      class="emergency-call"
      aria-label="Emergency call"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
        />
      </svg>
    </a>

    <!-- JavaScript files -->
    <script src="js/main.js"></script>
  </body>
</html>
