<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Contact Us - SaySomething</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#3B82F6",
              secondary: "#10B981",
              accent: "#F59E0B",
              light: "#F3F4F6",
              dark: "#1F2937",
            },
            fontFamily: {
              sans: ["Poppins", "sans-serif"],
              body: ["Lato", "sans-serif"],
            },
            screens: {
              sm: "640px",
              md: "901px",
              lg: "1024px",
              xl: "1280px",
              "2xl": "1536px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="css/styles.css" />
    <style type="text/tailwindcss">
      @layer utilities {
        .text-shadow {
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .transition-smooth {
          transition: all 0.3s ease-in-out;
        }
      }
      body.menu-open {
        overflow: hidden;
      }
    </style>
  </head>
  <body class="font-body bg-light text-dark page-transition" style="opacity: 0">
    <!-- Header/Navigation -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <div class="flex-shrink-0 flex items-center">
            <a href="/" class="flex items-center">
              <span class="font-sans font-bold text-2xl text-primary"
                >Say<span class="text-secondary">Something</span></span
              >
            </a>
          </div>

          <!-- Desktop Navigation -->
          <nav class="hidden md:flex space-x-8">
            <a
              href="index.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Home</a
            >
            <a
              href="resources.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Resources</a
            >
            <a
              href="awareness.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Awareness</a
            >
            <a
              href="report.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Report & Support</a
            >
            <a
              href="self-care.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Self-Care</a
            >
            <a
              href="about.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >About</a
            >
            <a
              href="contact.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Contact</a
            >
          </nav>

          <!-- Mobile menu button -->
          <div class="flex md:hidden">
            <button
              class="hamburger"
              aria-label="Toggle mobile menu"
              aria-expanded="false"
              aria-controls="mobile-menu"
            >
              <span></span>
              <span></span>
              <span></span>
              <span></span>
            </button>
          </div>
        </div>
      </div>

      <!-- Mobile menu container -->
      <div class="mobile-menu-container">
        <div class="flex flex-col items-center justify-center h-full">
          <a
            href="index.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Home</a
          >
          <a
            href="resources.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Resources</a
          >
          <a
            href="awareness.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Awareness</a
          >
          <a
            href="report.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Report & Support</a
          >
          <a
            href="self-care.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Self-Care</a
          >
          <a
            href="about.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >About</a
          >
          <a
            href="contact.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary transition-smooth"
            >Contact</a
          >

          <div class="absolute bottom-10 flex space-x-4">
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                />
              </svg>
            </a>
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                />
              </svg>
            </a>
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </header>

    <main>
      <!-- Hero Section -->
      <section
        class="relative bg-gradient-to-br from-primary/10 to-secondary/10 overflow-hidden"
      >
        <!-- Background pattern -->
        <div class="absolute inset-0 opacity-10">
          <svg
            class="w-full h-full"
            viewBox="0 0 100 100"
            preserveAspectRatio="none"
          >
            <defs>
              <pattern
                id="grid"
                width="8"
                height="8"
                patternUnits="userSpaceOnUse"
              >
                <path
                  d="M0 8L8 0"
                  stroke="currentColor"
                  stroke-width="0.5"
                  fill="none"
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
          </svg>
        </div>

        <div
          class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32 relative"
        >
          <div class="grid md:grid-cols-2 gap-12 items-center">
            <!-- Left content -->
            <div class="space-y-8 animate-fade-in">
              <div class="space-y-4">
                <h1
                  class="font-sans font-bold text-4xl md:text-5xl lg:text-6xl text-dark text-shadow leading-tight"
                >
                  Get in <span class="text-primary">Touch</span>
                </h1>
                <p class="text-lg md:text-xl text-gray-600 max-w-lg">
                  Have questions, feedback, or want to get involved? We'd love
                  to hear from you. Reach out to the SaySomething team.
                </p>
              </div>

              <div class="flex flex-wrap gap-4">
                <a
                  href="#contact-form"
                  class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-smooth hover-lift"
                >
                  Send a Message
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 ml-2"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </a>
                <a
                  href="#faq"
                  class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent transition-smooth hover-lift"
                >
                  View FAQs
                </a>
              </div>
            </div>

            <!-- Right content - Illustration -->
            <div
              class="relative hidden md:block animate-fade-in"
              style="animation-delay: 0.3s"
            >
              <div
                class="absolute -top-6 -right-6 w-24 h-24 bg-accent/20 rounded-full blur-2xl"
              ></div>
              <div
                class="absolute -bottom-8 -left-8 w-32 h-32 bg-primary/20 rounded-full blur-2xl"
              ></div>

              <div
                class="relative bg-white rounded-2xl shadow-xl overflow-hidden p-1 img-hover-zoom"
              >
                <div class="aspect-w-4 aspect-h-3 rounded-xl overflow-hidden">
                  <img
                    src="https://images.unsplash.com/photo-1557426272-fc759fdf7a8d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                    alt="Students discussing at a table"
                    class="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Contact Form Section -->
      <section id="contact-form" class="py-16 md:py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16 animate-fade-in">
            <h2 class="text-3xl md:text-4xl font-sans font-bold text-dark">
              Contact Us
            </h2>
            <div class="w-24 h-1 bg-primary mx-auto mt-4 rounded-full"></div>
            <p class="mt-6 text-lg text-gray-600 max-w-3xl mx-auto">
              Fill out the form below and we'll get back to you as soon as
              possible.
            </p>
          </div>

          <div class="grid md:grid-cols-2 gap-12 lg:gap-16">
            <!-- Contact Form -->
            <div class="bg-light rounded-xl p-8 shadow-sm">
              <form
                id="contact-form"
                action="/api/contact"
                method="POST"
                class="space-y-6"
              >
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <!-- First Name -->
                  <div>
                    <label
                      for="first-name"
                      class="block text-sm font-medium text-gray-700 mb-1"
                      >First Name</label
                    >
                    <input
                      type="text"
                      name="first-name"
                      id="first-name"
                      class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm p-3 border"
                      placeholder="Your first name"
                      required
                    />
                  </div>

                  <!-- Last Name -->
                  <div>
                    <label
                      for="last-name"
                      class="block text-sm font-medium text-gray-700 mb-1"
                      >Last Name</label
                    >
                    <input
                      type="text"
                      name="last-name"
                      id="last-name"
                      class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm p-3 border"
                      placeholder="Your last name"
                      required
                    />
                  </div>
                </div>

                <!-- Email -->
                <div>
                  <label
                    for="email"
                    class="block text-sm font-medium text-gray-700 mb-1"
                    >Email Address</label
                  >
                  <input
                    type="email"
                    name="email"
                    id="email"
                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm p-3 border"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <!-- Subject -->
                <div>
                  <label
                    for="subject"
                    class="block text-sm font-medium text-gray-700 mb-1"
                    >Subject</label
                  >
                  <select
                    name="subject"
                    id="subject"
                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm p-3 border"
                    required
                  >
                    <option value="" disabled selected>Select a subject</option>
                    <option value="general">General Inquiry</option>
                    <option value="feedback">Feedback</option>
                    <option value="volunteer">Volunteer Opportunities</option>
                    <option value="resources">Resource Suggestion</option>
                    <option value="technical">Technical Issue</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <!-- Message -->
                <div>
                  <label
                    for="message"
                    class="block text-sm font-medium text-gray-700 mb-1"
                    >Message</label
                  >
                  <textarea
                    name="message"
                    id="message"
                    rows="5"
                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm p-3 border"
                    placeholder="Type your message here..."
                    required
                  ></textarea>
                </div>

                <!-- Submit Button -->
                <div>
                  <button
                    type="submit"
                    class="w-full inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-smooth"
                  >
                    Send Message
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 ml-2"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11h2v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"
                      />
                    </svg>
                  </button>
                </div>
              </form>
            </div>

            <!-- Contact Info -->
            <div class="space-y-8">
              <div>
                <h3 class="text-2xl font-sans font-semibold text-dark mb-6">
                  Contact Information
                </h3>
                <p class="text-gray-600 mb-8">
                  Have questions or need assistance? Reach out to us using any
                  of the methods below, or fill out the contact form.
                </p>

                <div class="space-y-4">
                  <!-- Email -->
                  <div class="flex items-start">
                    <div class="flex-shrink-0">
                      <div
                        class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5 text-primary"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                    </div>
                    <div class="ml-4">
                      <h4 class="text-base font-medium text-gray-800">Email</h4>
                      <a
                        href="mailto:<EMAIL>"
                        class="text-primary hover:text-primary/80 transition-smooth"
                        ><EMAIL></a
                      >
                    </div>
                  </div>

                  <!-- Phone -->
                  <div class="flex items-start">
                    <div class="flex-shrink-0">
                      <div
                        class="w-10 h-10 bg-secondary/10 rounded-full flex items-center justify-center"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5 text-secondary"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                          />
                        </svg>
                      </div>
                    </div>
                    <div class="ml-4">
                      <h4 class="text-base font-medium text-gray-800">Phone</h4>
                      <p class="text-gray-600">+233 50 000 0000</p>
                      <p class="text-sm text-gray-500">
                        (Office hours: Mon-Fri, 9am-5pm)
                      </p>
                    </div>
                  </div>

                  <!-- Location -->
                  <div class="flex items-start">
                    <div class="flex-shrink-0">
                      <div
                        class="w-10 h-10 bg-accent/10 rounded-full flex items-center justify-center"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5 text-accent"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                          />
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                          />
                        </svg>
                      </div>
                    </div>
                    <div class="ml-4">
                      <h4 class="text-base font-medium text-gray-800">
                        Location
                      </h4>
                      <p class="text-gray-600">Student Affairs Office</p>
                      <p class="text-gray-600">Takoradi Technical University</p>
                      <p class="text-gray-600">Takoradi, Ghana</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Social Media -->
              <div>
                <h3 class="text-xl font-sans font-semibold text-dark mb-4">
                  Connect With Us
                </h3>
                <div class="flex space-x-4">
                  <a
                    href="#"
                    class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white hover:bg-blue-600 transition-smooth"
                  >
                    <span class="sr-only">Facebook</span>
                    <svg
                      class="h-5 w-5"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </a>
                  <a
                    href="#"
                    class="w-10 h-10 bg-pink-500 rounded-full flex items-center justify-center text-white hover:bg-pink-600 transition-smooth"
                  >
                    <span class="sr-only">Instagram</span>
                    <svg
                      class="h-5 w-5"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </a>
                  <a
                    href="#"
                    class="w-10 h-10 bg-sky-500 rounded-full flex items-center justify-center text-white hover:bg-sky-600 transition-smooth"
                  >
                    <span class="sr-only">Twitter</span>
                    <svg
                      class="h-5 w-5"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                      />
                    </svg>
                  </a>
                  <a
                    href="#"
                    class="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center text-white hover:bg-red-600 transition-smooth"
                  >
                    <span class="sr-only">YouTube</span>
                    <svg
                      class="h-5 w-5"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- FAQ Section -->
      <section id="faq" class="py-16 md:py-24 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16 animate-fade-in">
            <h2 class="text-3xl md:text-4xl font-sans font-bold text-dark">
              Frequently Asked Questions
            </h2>
            <div class="w-24 h-1 bg-accent mx-auto mt-4 rounded-full"></div>
            <p class="mt-6 text-lg text-gray-600 max-w-3xl mx-auto">
              Find answers to commonly asked questions about SaySomething and
              our services.
            </p>
          </div>

          <div class="max-w-3xl mx-auto">
            <div class="space-y-6">
              <!-- FAQ Item 1 -->
              <div class="bg-white rounded-xl p-6 shadow-sm">
                <button
                  class="flex justify-between items-center w-full text-left focus:outline-none"
                >
                  <h3 class="text-lg font-medium text-gray-900">
                    How can I get involved with SaySomething?
                  </h3>
                  <svg
                    class="h-5 w-5 text-gray-500"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
                <div class="mt-4 text-gray-600">
                  <p>
                    There are several ways to get involved with SaySomething:
                  </p>
                  <ul class="list-disc pl-5 mt-2 space-y-1">
                    <li>Volunteer as a peer supporter</li>
                    <li>Contribute content for our resources section</li>
                    <li>Help organize awareness events on campus</li>
                    <li>Provide technical support for our website</li>
                  </ul>
                  <p class="mt-2">
                    Fill out the contact form above and mention how you'd like
                    to get involved!
                  </p>
                </div>
              </div>

              <!-- FAQ Item 2 -->
              <div class="bg-white rounded-xl p-6 shadow-sm">
                <button
                  class="flex justify-between items-center w-full text-left focus:outline-none"
                >
                  <h3 class="text-lg font-medium text-gray-900">
                    Is the information I share confidential?
                  </h3>
                  <svg
                    class="h-5 w-5 text-gray-500"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
                <div class="mt-4 text-gray-600">
                  <p>
                    Yes, we take privacy and confidentiality very seriously. Any
                    information you share through our contact forms or reporting
                    systems is kept strictly confidential and is only accessible
                    to authorized team members who need the information to
                    provide support.
                  </p>
                  <p class="mt-2">
                    For more details, please refer to our privacy policy.
                  </p>
                </div>
              </div>

              <!-- FAQ Item 3 -->
              <div class="bg-white rounded-xl p-6 shadow-sm">
                <button
                  class="flex justify-between items-center w-full text-left focus:outline-none"
                >
                  <h3 class="text-lg font-medium text-gray-900">
                    How quickly will I receive a response to my inquiry?
                  </h3>
                  <svg
                    class="h-5 w-5 text-gray-500"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
                <div class="mt-4 text-gray-600">
                  <p>
                    We aim to respond to all inquiries within 24-48 hours during
                    weekdays. For urgent matters related to mental health
                    crises, please use the emergency resources listed on our
                    Resources page or call the emergency helpline directly.
                  </p>
                </div>
              </div>

              <!-- FAQ Item 4 -->
              <div class="bg-white rounded-xl p-6 shadow-sm">
                <button
                  class="flex justify-between items-center w-full text-left focus:outline-none"
                >
                  <h3 class="text-lg font-medium text-gray-900">
                    Can I suggest a resource to be added to the website?
                  </h3>
                  <svg
                    class="h-5 w-5 text-gray-500"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
                <div class="mt-4 text-gray-600">
                  <p>
                    Absolutely! We welcome resource suggestions from our
                    community. Please use the contact form and select "Resource
                    Suggestion" as the subject. Include details about the
                    resource, why you think it would be valuable, and any
                    relevant links or contact information.
                  </p>
                  <p class="mt-2">
                    Our team will review all suggestions and add appropriate
                    resources to our directory.
                  </p>
                </div>
              </div>

              <!-- FAQ Item 5 -->
              <div class="bg-white rounded-xl p-6 shadow-sm">
                <button
                  class="flex justify-between items-center w-full text-left focus:outline-none"
                >
                  <h3 class="text-lg font-medium text-gray-900">
                    Is SaySomething affiliated with the university?
                  </h3>
                  <svg
                    class="h-5 w-5 text-gray-500"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
                <div class="mt-4 text-gray-600">
                  <p>
                    Yes, SaySomething is a collaborative initiative supported by
                    Takoradi Technical University. We work closely with the
                    university's Counseling Center, Student Affairs Office, and
                    Department of Psychology to ensure our resources are
                    accurate and aligned with the support services available on
                    campus.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid md:grid-cols-4 gap-8">
          <!-- Logo and description -->
          <div class="col-span-1 md:col-span-2">
            <a href="/" class="flex items-center">
              <span class="font-sans font-bold text-2xl text-white"
                >Say<span class="text-secondary">Something</span></span
              >
            </a>
            <p class="mt-4 text-gray-300">
              A centralized, student-friendly platform designed to raise mental
              health awareness, guide students to relevant support systems, and
              reduce stigma on university campuses.
            </p>
          </div>

          <!-- Quick links -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="index.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Home</a
                >
              </li>
              <li>
                <a
                  href="resources.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Resources</a
                >
              </li>
              <li>
                <a
                  href="awareness.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Awareness</a
                >
              </li>
              <li>
                <a
                  href="report.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Report & Support</a
                >
              </li>
              <li>
                <a
                  href="self-care.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Self-Care</a
                >
              </li>
            </ul>
          </div>

          <!-- Contact -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Contact</h3>
            <ul class="space-y-2">
              <li class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
                <a
                  href="mailto:<EMAIL>"
                  class="text-gray-300 hover:text-white transition-smooth"
                  ><EMAIL></a
                >
              </li>
              <li class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
                <span class="text-gray-300"
                  >Takoradi Technical University, Ghana</span
                >
              </li>
            </ul>

            <div class="mt-4 flex space-x-4">
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Facebook</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                    clip-rule="evenodd"
                  />
                </svg>
              </a>
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Instagram</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                    clip-rule="evenodd"
                  />
                </svg>
              </a>
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Twitter</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                  />
                </svg>
              </a>
            </div>
          </div>
        </div>

        <div
          class="mt-12 pt-8 border-t border-gray-800 text-center text-gray-400 text-sm"
        >
          <p>Ransford Quayson | BT/ITW/24/009</p>
          <p>&copy; 2025 SaySomething. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <!-- Emergency Call Button -->
    <a
      href="tel:+233500000000"
      class="emergency-call"
      aria-label="Emergency call"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
        />
      </svg>
    </a>

    <!-- JavaScript files -->
    <script src="js/main.js"></script>
    <script src="js/forms.js"></script>
  </body>
</html>
