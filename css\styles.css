/* Custom styles beyond Tailwind */

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Page transitions */
body.page-transition {
    transition: opacity 0.3s ease;
}

/* Subtle animations */
.hover-lift {
    transition: transform 0.3s ease;
}

.hero {
    background-image: url(../images/ttu-image.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.hover-lift:hover {
    transform: translateY(-5px);
}

/* Custom focus styles for better accessibility */
a:focus,
button:focus {
    outline: 2px solid rgba(59, 130, 246, 0.5);
    outline-offset: 2px;
}

/* Gradient text effect */
.gradient-text {
    background: linear-gradient(90deg, #3B82F6, #10B981);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

/* Image hover effects */
.img-hover-zoom {
    overflow: hidden;
}

.img-hover-zoom img {
    transition: transform 0.5s ease;
}

.img-hover-zoom:hover img {
    transform: scale(1.05);
}

/* Custom animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Staggered animation delays for children */
.stagger-children>* {
    opacity: 0;
    animation: fadeIn 0.5s ease-out forwards;
}

.stagger-children>*:nth-child(1) {
    animation-delay: 0.1s;
}

.stagger-children>*:nth-child(2) {
    animation-delay: 0.2s;
}

.stagger-children>*:nth-child(3) {
    animation-delay: 0.3s;
}

.stagger-children>*:nth-child(4) {
    animation-delay: 0.4s;
}

.stagger-children>*:nth-child(5) {
    animation-delay: 0.5s;
}

.stagger-children>*:nth-child(6) {
    animation-delay: 0.6s;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f3f4f6;
}

::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Mobile Menu Styles */
.hamburger {
    width: 30px;
    height: 24px;
    position: relative;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    z-index: 100;
}

.hamburger span {
    display: block;
    height: 2px;
    width: 100%;
    background-color: #1F2937;
    transition: all 0.3s ease-in-out;
}

.hamburger.active span:nth-child(1) {
    transform: translateY(11px) rotate(45deg);
}

.hamburger.active span:nth-child(2),
.hamburger.active span:nth-child(3) {
    opacity: 0;
}

.hamburger.active span:nth-child(4) {
    transform: translateY(-11px) rotate(-45deg);
}

.hamburger.active span {
    background-color: #1F2937;
}

.mobile-menu-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    background-color: #fff;
    z-index: 90;
    overflow: hidden;
    transition: height 0.4s cubic-bezier(0.77, 0, 0.175, 1);
    opacity: 0;
    visibility: hidden;
}

.mobile-menu-container.active {
    height: 100vh;
    opacity: 1;
    visibility: visible;
}

/* Floating emergency call button */
.emergency-call {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #F59E0B, #DC2626);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.15);
    z-index: 50;
    transition: all 0.3s ease;
}

.emergency-call:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.emergency-call svg {
    color: white;
    width: 28px;
    height: 28px;
}

/* Emergency call pulse effect */
.emergency-call::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: rgba(245, 158, 11, 0.3);
    z-index: -1;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.7;
    }

    70% {
        transform: scale(1.3);
        opacity: 0;
    }

    100% {
        transform: scale(1.3);
        opacity: 0;
    }
}