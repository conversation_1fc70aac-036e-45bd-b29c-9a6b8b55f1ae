<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SaySomething - Mental Health Support for Students</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#3B82F6",
              secondary: "#10B981",
              accent: "#F59E0B",
              light: "#F3F4F6",
              dark: "#1F2937",
            },
            fontFamily: {
              sans: ["Poppins", "sans-serif"],
              body: ["Lato", "sans-serif"],
            },
            screens: {
              sm: "640px",
              md: "901px",
              lg: "1024px",
              xl: "1280px",
              "2xl": "1536px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="css/styles.css" />
    <style type="text/tailwindcss">
      @layer utilities {
        .text-shadow {
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .transition-smooth {
          transition: all 0.3s ease-in-out;
        }
      }
      body.menu-open {
        overflow: hidden;
      }
    </style>
  </head>
  <body class="font-body bg-light text-dark page-transition" style="opacity: 0">
    <!-- Header/Navigation -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <div class="flex-shrink-0 flex items-center">
            <a href="/" class="flex items-center">
              <span class="font-sans font-bold text-2xl text-primary"
                >Say<span class="text-secondary">Something</span></span
              >
            </a>
          </div>

          <!-- Desktop Navigation -->
          <nav class="hidden md:flex space-x-8">
            <a
              href="index.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Home</a
            >
            <a
              href="resources.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Resources</a
            >
            <a
              href="awareness.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Awareness</a
            >
            <a
              href="report.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Report & Support</a
            >
            <a
              href="self-care.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Self-Care</a
            >
            <a
              href="about.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >About</a
            >
            <a
              href="contact.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Contact</a
            >
          </nav>

          <!-- Mobile menu button -->
          <div class="flex md:hidden">
            <button
              class="hamburger"
              aria-label="Toggle mobile menu"
              aria-expanded="false"
              aria-controls="mobile-menu"
            >
              <span></span>
              <span></span>
              <span></span>
              <span></span>
            </button>
          </div>
        </div>
      </div>

      <!-- Mobile menu container -->
      <div class="mobile-menu-container">
        <div class="flex flex-col items-center justify-center h-full">
          <a
            href="index.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Home</a
          >
          <a
            href="resources.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Resources</a
          >
          <a
            href="awareness.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Awareness</a
          >
          <a
            href="report.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Report & Support</a
          >
          <a
            href="self-care.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Self-Care</a
          >
          <a
            href="about.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >About</a
          >
          <a
            href="contact.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary transition-smooth"
            >Contact</a
          >

          <div class="absolute bottom-10 flex space-x-4">
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                />
              </svg>
            </a>
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                />
              </svg>
            </a>
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </header>

    <!-- Main content will go here -->

    <main>
      <!-- Hero Section -->
      <section
        class="relative bg-gradient-to-br from-primary/10 to-secondary/10 overflow-hidden"
      >
        <!-- Background pattern -->
        <div class="absolute inset-0 opacity-10 hero">
          <svg
            class="w-full h-full"
            viewBox="0 0 100 100"
            preserveAspectRatio="none"
          >
            <defs>
              <pattern
                id="grid"
                width="8"
                height="8"
                patternUnits="userSpaceOnUse"
              >
                <path
                  d="M0 8L8 0"
                  stroke="currentColor"
                  stroke-width="0.5"
                  fill="none"
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
          </svg>
        </div>

        <div
          class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32 relative"
        >
          <div class="grid md:grid-cols-2 gap-12 items-center">
            <!-- Left content -->
            <div class="space-y-8 animate-fade-in">
              <div class="space-y-4">
                <h1
                  class="font-sans font-bold text-4xl md:text-5xl lg:text-6xl text-dark text-shadow leading-tight"
                >
                  Your mental health <span class="gradient-text">matters</span>.
                </h1>
                <p class="text-lg md:text-xl text-gray-600 max-w-lg">
                  A safe space for university students to find support,
                  resources, and community for mental health and wellbeing.
                </p>
              </div>

              <div class="flex flex-wrap gap-4">
                <a
                  href="#"
                  class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-smooth hover-lift"
                >
                  Explore Resources
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 ml-2"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </a>
                <a
                  href="#"
                  class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary transition-smooth hover-lift"
                >
                  Get Support
                </a>
              </div>
            </div>

            <!-- Right content - Illustration -->
            <div
              class="relative hidden md:block animate-fade-in"
              style="animation-delay: 0.3s"
            >
              <div
                class="absolute -top-6 -right-6 w-24 h-24 bg-accent/20 rounded-full blur-2xl"
              ></div>
              <div
                class="absolute -bottom-8 -left-8 w-32 h-32 bg-primary/20 rounded-full blur-2xl"
              ></div>

              <div
                class="relative bg-white rounded-2xl shadow-xl overflow-hidden p-1 img-hover-zoom"
              >
                <div
                  class="aspect-w-4 aspect-h-3 rounded-xl overflow-hidden"
                  style="height: 25rem"
                >
                  <img
                    src="./images/ttu.png"
                    alt="Students supporting each other"
                    class="w-full h-full object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- What is Mental Health Section -->
      <section class="py-16 md:py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16 animate-fade-in">
            <h2 class="text-3xl md:text-4xl font-sans font-bold text-dark">
              What is Mental Health?
            </h2>
            <div class="w-24 h-1 bg-secondary mx-auto mt-4 rounded-full"></div>
          </div>

          <div class="grid md:grid-cols-3 gap-8 md:gap-12 stagger-children">
            <!-- Emotional Well-being -->
            <div
              class="bg-light rounded-xl p-8 shadow-sm hover:shadow-md transition-smooth hover-lift"
            >
              <div
                class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-6"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-8 w-8 text-primary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h3 class="text-xl font-sans font-semibold mb-3">
                Emotional Well-being
              </h3>
              <p class="text-gray-600">
                Mental health encompasses our emotional responses to life's
                experiences and our ability to manage feelings like joy,
                sadness, and stress in healthy ways.
              </p>
            </div>

            <!-- Psychological Health -->
            <div
              class="bg-light rounded-xl p-8 shadow-sm hover:shadow-md transition-smooth hover-lift"
            >
              <div
                class="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center mb-6"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-8 w-8 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                  />
                </svg>
              </div>
              <h3 class="text-xl font-sans font-semibold mb-3">
                Psychological Health
              </h3>
              <p class="text-gray-600">
                This involves how we think, process information, and make
                decisions. Good psychological health allows us to handle life's
                challenges with resilience and clarity.
              </p>
            </div>

            <!-- Social Well-being -->
            <div
              class="bg-light rounded-xl p-8 shadow-sm hover:shadow-md transition-smooth hover-lift"
            >
              <div
                class="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mb-6"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-8 w-8 text-accent"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                  />
                </svg>
              </div>
              <h3 class="text-xl font-sans font-semibold mb-3">
                Social Well-being
              </h3>
              <p class="text-gray-600">
                Our ability to form and maintain healthy relationships, connect
                with others, and contribute to our communities is a vital
                component of overall mental health.
              </p>
            </div>
          </div>

          <div
            class="mt-12 text-center animate-fade-in"
            style="animation-delay: 0.6s"
          >
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
              Mental health exists on a spectrum and changes throughout our
              lives. Just like physical health, it requires attention, care, and
              sometimes professional support.
            </p>
          </div>
        </div>
      </section>

      <!-- Why This Platform Exists Section -->
      <section class="py-16 md:py-24 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16 animate-fade-in">
            <h2 class="text-3xl md:text-4xl font-sans font-bold text-dark">
              Why This Platform Exists
            </h2>
            <div class="w-24 h-1 bg-primary mx-auto mt-4 rounded-full"></div>
            <p class="mt-6 text-lg text-gray-600 max-w-3xl mx-auto">
              University students face unique challenges that can impact mental
              wellbeing.
            </p>
          </div>

          <div
            class="grid md:grid-cols-2 gap-8 lg:gap-16 items-center stagger-children"
          >
            <!-- Left side - Image -->
            <div class="relative">
              <div
                class="absolute -top-6 -left-6 w-24 h-24 bg-primary/20 rounded-full blur-2xl"
              ></div>
              <div
                class="absolute -bottom-8 -right-8 w-32 h-32 bg-secondary/20 rounded-full blur-2xl"
              ></div>

              <div
                class="relative bg-white rounded-2xl shadow-xl overflow-hidden p-1 img-hover-zoom"
              >
                <div class="aspect-w-4 aspect-h-3 rounded-xl overflow-hidden">
                  <img
                    src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                    alt="Students experiencing stress"
                    class="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>

            <!-- Right side - Content -->
            <div class="space-y-8">
              <!-- Challenge 1 -->
              <div class="flex gap-4">
                <div
                  class="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-sans font-semibold mb-2">
                    Academic Pressure
                  </h3>
                  <p class="text-gray-600">
                    The constant pressure of exams, assignments, and academic
                    performance can lead to stress, anxiety, and burnout among
                    students.
                  </p>
                </div>
              </div>

              <!-- Challenge 2 -->
              <div class="flex gap-4">
                <div
                  class="flex-shrink-0 w-12 h-12 bg-secondary/10 rounded-full flex items-center justify-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-secondary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-sans font-semibold mb-2">
                    Social Stigma
                  </h3>
                  <p class="text-gray-600">
                    Many students hesitate to seek help due to fear of judgment,
                    misunderstanding, or the persistent stigma around mental
                    health issues.
                  </p>
                </div>
              </div>

              <!-- Challenge 3 -->
              <div class="flex gap-4">
                <div
                  class="flex-shrink-0 w-12 h-12 bg-accent/10 rounded-full flex items-center justify-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-accent"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-sans font-semibold mb-2">
                    Resource Confusion
                  </h3>
                  <p class="text-gray-600">
                    Students often don't know where to turn for help, what
                    resources are available, or how to navigate complex support
                    systems on campus.
                  </p>
                </div>
              </div>

              <!-- Challenge 4 -->
              <div class="flex gap-4">
                <div
                  class="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 class="text-xl font-sans font-semibold mb-2">
                    Safety Concerns
                  </h3>
                  <p class="text-gray-600">
                    Issues like sexual harassment require specialized support,
                    yet many students lack clear pathways to report incidents
                    and access appropriate help.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Our Mission Section -->
      <section class="py-16 md:py-24 bg-white overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <!-- Background decoration -->
          <div
            class="absolute top-1/4 right-0 w-64 h-64 bg-primary/5 rounded-full blur-3xl -z-10"
          ></div>
          <div
            class="absolute bottom-1/4 left-0 w-64 h-64 bg-secondary/5 rounded-full blur-3xl -z-10"
          ></div>

          <div
            class="text-center mb-16 animate-fade-in"
            style="animation-delay: 0.9s"
          >
            <h2 class="text-3xl md:text-4xl font-sans font-bold text-dark">
              Our Mission
            </h2>
            <div class="w-24 h-1 bg-accent mx-auto mt-4 rounded-full"></div>
          </div>

          <div
            class="grid md:grid-cols-2 gap-12 lg:gap-16 items-center stagger-children"
          >
            <!-- Left side - Content -->
            <div class="space-y-6">
              <h3 class="text-2xl font-sans font-semibold text-dark">
                Creating a supportive community for every student
              </h3>
              <p class="text-lg text-gray-600">
                SaySomething exists to transform how university students access
                mental health support. We're building a platform that is:
              </p>

              <div class="space-y-4">
                <!-- Mission point 1 -->
                <div class="flex items-start gap-3">
                  <div
                    class="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mt-1"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 text-white"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <p class="text-gray-700">
                    <strong class="font-medium">Accessible</strong> — Available
                    to all students regardless of background, with a
                    mobile-first approach that meets students where they are.
                  </p>
                </div>

                <!-- Mission point 2 -->
                <div class="flex items-start gap-3">
                  <div
                    class="flex-shrink-0 w-6 h-6 bg-secondary rounded-full flex items-center justify-center mt-1"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 text-white"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <p class="text-gray-700">
                    <strong class="font-medium">Centralized</strong> — Bringing
                    together all campus and community resources in one
                    easy-to-navigate platform.
                  </p>
                </div>

                <!-- Mission point 3 -->
                <div class="flex items-start gap-3">
                  <div
                    class="flex-shrink-0 w-6 h-6 bg-accent rounded-full flex items-center justify-center mt-1"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 text-white"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <p class="text-gray-700">
                    <strong class="font-medium">Empathetic</strong> — Designed
                    with real student experiences in mind, using language that
                    resonates and destigmatizes.
                  </p>
                </div>

                <!-- Mission point 4 -->
                <div class="flex items-start gap-3">
                  <div
                    class="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mt-1"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 text-white"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <p class="text-gray-700">
                    <strong class="font-medium">Informative</strong> — Providing
                    evidence-based resources that educate and empower students
                    to take charge of their mental health.
                  </p>
                </div>
              </div>

              <div class="pt-4">
                <a
                  href="#"
                  class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-smooth hover-lift"
                >
                  Learn More About Us
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 ml-2"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </a>
              </div>
            </div>

            <!-- Right side - Image -->
            <div
              class="relative order-first md:order-last animate-fade-in"
              style="animation-delay: 0.9s"
            >
              <div
                class="absolute -top-6 -right-6 w-24 h-24 bg-accent/20 rounded-full blur-2xl"
              ></div>
              <div
                class="absolute -bottom-8 -left-8 w-32 h-32 bg-primary/20 rounded-full blur-2xl"
              ></div>

              <div
                class="relative bg-white rounded-2xl shadow-xl overflow-hidden p-1 img-hover-zoom"
              >
                <div class="aspect-w-1 aspect-h-1 rounded-xl overflow-hidden">
                  <img
                    src="https://images.unsplash.com/photo-1571624436279-b272aff752b5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                    alt="Students supporting each other"
                    class="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Navigation Icons Section -->
      <section class="py-16 md:py-24 bg-gradient-to-b from-gray-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div
            class="text-center mb-16 animate-fade-in"
            style="animation-delay: 1.2s"
          >
            <h2 class="text-3xl md:text-4xl font-sans font-bold text-dark">
              Quick Access
            </h2>
            <div class="w-24 h-1 bg-secondary mx-auto mt-4 rounded-full"></div>
            <p class="mt-6 text-lg text-gray-600 max-w-3xl mx-auto">
              Find the support you need with our easy-to-navigate resource
              sections.
            </p>
          </div>

          <div
            class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 stagger-children"
          >
            <!-- Icon 1 -->
            <a href="#" class="flex flex-col items-center group hover-lift">
              <div
                class="w-20 h-20 bg-primary/10 rounded-2xl flex items-center justify-center mb-4 group-hover:bg-primary/20 transition-smooth"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-10 w-10 text-primary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                  />
                </svg>
              </div>
              <span
                class="text-lg font-medium text-dark group-hover:text-primary transition-smooth"
                >Resources</span
              >
            </a>

            <!-- Icon 2 -->
            <a href="#" class="flex flex-col items-center group hover-lift">
              <div
                class="w-20 h-20 bg-secondary/10 rounded-2xl flex items-center justify-center mb-4 group-hover:bg-secondary/20 transition-smooth"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-10 w-10 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                  />
                </svg>
              </div>
              <span
                class="text-lg font-medium text-dark group-hover:text-secondary transition-smooth"
                >Awareness</span
              >
            </a>

            <!-- Icon 3 -->
            <a href="#" class="flex flex-col items-center group hover-lift">
              <div
                class="w-20 h-20 bg-accent/10 rounded-2xl flex items-center justify-center mb-4 group-hover:bg-accent/20 transition-smooth"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-10 w-10 text-accent"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
              </div>
              <span
                class="text-lg font-medium text-dark group-hover:text-accent transition-smooth"
                >Report</span
              >
            </a>

            <!-- Icon 4 -->
            <a href="#" class="flex flex-col items-center group hover-lift">
              <div
                class="w-20 h-20 bg-primary/10 rounded-2xl flex items-center justify-center mb-4 group-hover:bg-primary/20 transition-smooth"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-10 w-10 text-primary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                  />
                </svg>
              </div>
              <span
                class="text-lg font-medium text-dark group-hover:text-primary transition-smooth"
                >Self-Care</span
              >
            </a>

            <!-- Icon 5 -->
            <a href="#" class="flex flex-col items-center group hover-lift">
              <div
                class="w-20 h-20 bg-secondary/10 rounded-2xl flex items-center justify-center mb-4 group-hover:bg-secondary/20 transition-smooth"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-10 w-10 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                  />
                </svg>
              </div>
              <span
                class="text-lg font-medium text-dark group-hover:text-secondary transition-smooth"
                >Support Groups</span
              >
            </a>

            <!-- Icon 6 -->
            <a href="#" class="flex flex-col items-center group hover-lift">
              <div
                class="w-20 h-20 bg-accent/10 rounded-2xl flex items-center justify-center mb-4 group-hover:bg-accent/20 transition-smooth"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-10 w-10 text-accent"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <span
                class="text-lg font-medium text-dark group-hover:text-accent transition-smooth"
                >About Us</span
              >
            </a>
          </div>

          <div
            class="mt-16 text-center animate-fade-in"
            style="animation-delay: 1.5s"
          >
            <div
              class="inline-flex items-center px-4 py-2 bg-gray-100 rounded-full text-sm font-medium text-gray-600"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2 text-primary"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
                />
              </svg>
              Mobile-optimized experience for access anywhere, anytime
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid md:grid-cols-4 gap-8">
          <!-- Logo and description -->
          <div class="col-span-1 md:col-span-2">
            <a href="/" class="flex items-center">
              <span class="font-sans font-bold text-2xl text-white"
                >Say<span class="text-secondary">Something</span></span
              >
            </a>
            <p class="mt-4 text-gray-300">
              A centralized, student-friendly platform designed to raise mental
              health awareness, guide students to relevant support systems, and
              reduce stigma on university campuses.
            </p>
          </div>

          <!-- Quick links -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="#"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Home</a
                >
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Resources</a
                >
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Awareness</a
                >
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Report & Support</a
                >
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Self-Care</a
                >
              </li>
            </ul>
          </div>

          <!-- Contact -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Contact</h3>
            <ul class="space-y-2">
              <li class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
                <a
                  href="mailto:<EMAIL>"
                  class="text-gray-300 hover:text-white transition-smooth"
                  ><EMAIL></a
                >
              </li>
              <li class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
                <span class="text-gray-300"
                  >Takoradi Technical University, Ghana</span
                >
              </li>
            </ul>

            <div class="mt-4 flex space-x-4">
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Facebook</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                    clip-rule="evenodd"
                  />
                </svg>
              </a>
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Instagram</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                    clip-rule="evenodd"
                  />
                </svg>
              </a>
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Twitter</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                  />
                </svg>
              </a>
            </div>
          </div>
        </div>

        <div
          class="mt-12 pt-8 border-t border-gray-800 text-center text-gray-400 text-sm"
        >
          <p>Ransford Quayson | BT/ITW/24/009</p>
          <p>&copy; 2025 SaySomething. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <!-- Emergency Call Button -->
    <a
      href="tel:+233500000000"
      class="emergency-call"
      aria-label="Emergency call"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
        />
      </svg>
    </a>

    <!-- JavaScript files -->
    <script src="js/main.js"></script>
  </body>
</html>
