// SaySomething - Main JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize page transition
    initPageTransition();
    
    // Initialize animations on scroll
    initScrollAnimations();
    
    // Initialize smooth scrolling for anchor links
    initSmoothScroll();
    
    // Initialize mobile menu toggle
    initMobileMenu();
});

// Page transition effect
function initPageTransition() {
    // Fade in the page
    document.body.style.opacity = '1';
    
    // Handle links for page transitions
    document.querySelectorAll('a[href]:not([href^="#"]):not([target="_blank"])').forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            // Skip if modifier keys are pressed
            if (e.metaKey || e.ctrlKey || e.shiftKey || e.altKey) return;
            
            // Skip if it's an external link
            if (href.indexOf('http') === 0 && !href.includes(window.location.hostname)) return;
            
            e.preventDefault();
            
            // Fade out
            document.body.style.opacity = '0';
            
            // Navigate after transition
            setTimeout(() => {
                window.location.href = href;
            }, 300);
        });
    });
}

// Animate elements when they come into view
function initScrollAnimations() {
    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    
    if (!animatedElements.length) return;
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });
    
    animatedElements.forEach(element => {
        observer.observe(element);
    });
}

// Smooth scrolling for anchor links
function initSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            const targetId = this.getAttribute('href');
            
            if (targetId === '#') return;
            
            e.preventDefault();
            
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Update URL without page jump
                history.pushState(null, null, targetId);
                
                // Close mobile menu if open
                const mobileMenuContainer = document.querySelector('.mobile-menu-container');
                if (mobileMenuContainer && mobileMenuContainer.classList.contains('active')) {
                    toggleMobileMenu();
                }
            }
        });
    });
}

// Mobile menu functionality
function initMobileMenu() {
    const hamburger = document.querySelector('.hamburger');
    const mobileMenuContainer = document.querySelector('.mobile-menu-container');
    
    if (!hamburger || !mobileMenuContainer) return;
    
    hamburger.addEventListener('click', function() {
        toggleMobileMenu();
    });
    
    // Close menu when clicking menu items
    document.querySelectorAll('.mobile-menu-container .nav-item').forEach(item => {
        item.addEventListener('click', function() {
            toggleMobileMenu();
        });
    });
    
    // Close menu with escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && mobileMenuContainer.classList.contains('active')) {
            toggleMobileMenu();
        }
    });
}

function toggleMobileMenu() {
    const hamburger = document.querySelector('.hamburger');
    const mobileMenuContainer = document.querySelector('.mobile-menu-container');
    
    hamburger.classList.toggle('active');
    mobileMenuContainer.classList.toggle('active');
    document.body.classList.toggle('menu-open');
} 