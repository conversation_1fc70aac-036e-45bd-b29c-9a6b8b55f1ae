// SaySomething - Main JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize animations on scroll
    initScrollAnimations();
    
    // Initialize smooth scrolling for anchor links
    initSmoothScroll();
    
    // Initialize mobile menu toggle
    initMobileMenu();
    
    // Initialize page transitions
    initPageTransitions();
    
    // Add animation classes to elements with data-animate attribute
    initCustomAnimations();
});

// Animate elements when they come into view
function initScrollAnimations() {
    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    
    if (!animatedElements.length) return;
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const animationType = entry.target.dataset.animation || 'animate-fade-in';
                entry.target.classList.add(animationType);
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });
    
    animatedElements.forEach(element => {
        observer.observe(element);
    });
}

// Initialize custom animations based on data attributes
function initCustomAnimations() {
    const animatedElements = document.querySelectorAll('[data-animate]');
    
    if (!animatedElements.length) return;
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const animationType = entry.target.dataset.animate;
                entry.target.classList.add(animationType);
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });
    
    animatedElements.forEach(element => {
        observer.observe(element);
    });
}

// Smooth scrolling for anchor links
function initSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            const targetId = this.getAttribute('href');
            
            if (targetId === '#') return;
            
            e.preventDefault();
            
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                // Close mobile menu if it's open
                const mobileMenu = document.querySelector('.mobile-menu-container');
                const hamburger = document.querySelector('.hamburger');
                if (mobileMenu && mobileMenu.classList.contains('active')) {
                    mobileMenu.classList.remove('active');
                    hamburger.classList.remove('open');
                    document.body.classList.remove('menu-open');
                }
                
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Update URL without page jump
                history.pushState(null, null, targetId);
            }
        });
    });
}

// Mobile menu functionality
function initMobileMenu() {
    const hamburger = document.querySelector('.hamburger');
    const mobileMenu = document.querySelector('.mobile-menu-container');
    
    if (!hamburger || !mobileMenu) return;
    
    // Set initial state
    const navItems = mobileMenu.querySelectorAll('.nav-item');
    navItems.forEach((item, index) => {
        item.style.setProperty('--item-index', index);
    });
    
    hamburger.addEventListener('click', function() {
        this.classList.toggle('open');
        mobileMenu.classList.toggle('active');
        document.body.classList.toggle('menu-open');
    });
    
    // Close menu when clicking on a menu item
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            hamburger.classList.remove('open');
            mobileMenu.classList.remove('active');
            document.body.classList.remove('menu-open');
        });
    });
    
    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!hamburger.contains(e.target) && 
            !mobileMenu.contains(e.target) && 
            mobileMenu.classList.contains('active')) {
            hamburger.classList.remove('open');
            mobileMenu.classList.remove('active');
            document.body.classList.remove('menu-open');
        }
    });
}

// Page transitions
function initPageTransitions() {
    document.addEventListener('click', function(e) {
        // Only process links to other pages (not anchor links)
        const link = e.target.closest('a');
        if (!link) return;
        
        const href = link.getAttribute('href');
        if (!href || href.startsWith('#') || href.startsWith('mailto:') || href.startsWith('tel:')) return;
        
        // Check if it's an internal link
        if (link.hostname === window.location.hostname) {
            e.preventDefault();
            
            // Fade out
            document.body.classList.add('page-transition');
            document.body.style.opacity = '0';
            
            // Navigate after animation
            setTimeout(() => {
                window.location.href = href;
            }, 300);
        }
    });
    
    // Fade in on page load
    window.addEventListener('pageshow', function() {
        document.body.classList.add('page-transition');
        document.body.style.opacity = '1';
    });
}

// Add accessibility support for keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const mobileMenu = document.querySelector('.mobile-menu-container');
        const hamburger = document.querySelector('.hamburger');
        
        if (mobileMenu && mobileMenu.classList.contains('active')) {
            hamburger.classList.remove('open');
            mobileMenu.classList.remove('active');
            document.body.classList.remove('menu-open');
        }
    }
}); 