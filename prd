Product Requirements Document (PRD)

Product Name: SaySomething

Product Summary:
SaySomething is a centralized, student-friendly website designed to raise mental health awareness, guide students to relevant support systems, and reduce stigma on university campuses. The platform is informative, accessible, mobile-optimized, and includes a dedicated space for handling sexual harassment concerns. While primarily targeted at students of Takoradi Technical University (TTU) in Ghana, it is open and accessible to students from other institutions as well.

1. Target Users

University students (primarily from TTU - Takoradi Technical University, Ghana)

Student peer counselors

Student Union members

Campus support stakeholders (Counseling Center, Health Services, etc.)

2. Website Pages & Sections

A. Home Page

A welcoming page introducing the purpose and impact of SaySomething.

Sections:

Hero Section – Headline, subheading, and call-to-action buttons ("Explore Resources", "Get Support")

What is Mental Health? – Explains emotional, psychological, and social well-being

Why This Platform Exists – Describes the core problem: stress, burnout, stigma, confusion

Our Mission – Outline the solution: accessible, centralized, empathetic support

Navigation Icons – Visual buttons linking to Resources, Wellness, Reporting, etc.

Mobile Experience Note – Explains platform is mobile-first and friendly

Introduction Video or Slideshow – Optional media for engagement

B. Resources Directory

Provides a comprehensive, searchable catalog of campus and external mental health resources.

Sections:

Search & Filter Bar – By type, cost, location (on/off campus), etc.

Counseling Services – Descriptions and contact info

Peer Support Groups – Listings and meeting schedules

Crisis Support – 24/7 hotlines and emergency resources

Off-Campus Partners – NGOs, public mental health services

Academic Adjustments Info – How to request support for exams/classes

Resource Submission Form – Allow student suggestions

C. Awareness & Destigmatization Hub

Focuses on educating and inspiring through real stories and evidence-based facts.

Sections:

Student Stories – Anonymized testimonials

Myth vs. Fact – Clarify misconceptions

Signs & Symptoms – When to seek support

When to Get Help – Red flags and helpful prompts

Mental Health Spectrum – Normalize mental health fluctuations

Video Snippets from Students/Experts – Optional dynamic content

Call to Share Your Story – With consent form

D. Report & Support (Sexual Harassment Hub)

Dedicated to educating students on how to report sexual harassment and seek help.

Sections:

What is Sexual Harassment? – Definitions and examples

Clear Reporting Paths – Anonymous and official options

Immediate Support – Trauma counselors, peer advocates

Rights Education – Legal, academic, and medical options

Safety Planning Resources – Housing/class changes, relocation guides

Survivor Stories (Optional) – Verified and ethically collected

Report Now CTA – Clear, reassuring button

E. Self-Care & Wellness Page

Promotes resilience, mindfulness, and practical self-care habits.

Sections:

Stress Management Techniques – Journaling, breathing, scheduling

Improving Sleep – Sleep hygiene practices

Building Resilience – Growth mindset, support systems

Mindfulness Practices – Guided videos, grounding exercises

Self-Care Routine Builder – Downloadable worksheet

Wellness FAQs – Based on student concerns

Emergency Help Disclaimer – Highlight limitations

F. About & Partners Page

Explains the site’s background and builds trust.

Sections:

Who Built SaySomething – Project motivation and goals

Partner Organizations – Counseling center, Health Office, Active Minds

Acknowledgments – Contributors and institutions

Privacy & Ethics Statement – Consent, anonymity, and data use

Feedback Form – Help us improve

Contact Info – Email, phone (if available)

Social Media Links – If applicable

3. Platform Requirements

No login required (unless future features require it)

Responsive design (mobile-first)

Non-clinical language (warm, inclusive, simple)

Search/filter functionality on the directory page

Consent-driven story submission system

Static or low-maintenance dynamic site (no complex backend initially)

Inquiries sent via email using Nodemailer – All contact forms should be wired to trigger Nodemailer for secure email delivery to site administrators

4. Visual and UX Standards

Color palette: calming (blue, green, off-white)

Typography: modern and accessible fonts (e.g., Poppins, Lato)

Iconography: represent mental health topics with clarity and empathy

Tone: supportive, human, non-judgmental

Accessibility: alt-text for images, screen-reader support

5. Success Criteria

Students can clearly identify where to find help

Platform destigmatizes mental health conversations

Campus stakeholders adopt/link to the platform

Users find content easy to navigate on mobile

Contact forms reliably send messages via Nodemailer to admins

End of PRD

