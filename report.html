<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Report & Support - SaySomething</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#3B82F6",
              secondary: "#10B981",
              accent: "#F59E0B",
              light: "#F3F4F6",
              dark: "#1F2937",
            },
            fontFamily: {
              sans: ["Poppins", "sans-serif"],
              body: ["Lato", "sans-serif"],
            },
            screens: {
              sm: "640px",
              md: "901px",
              lg: "1024px",
              xl: "1280px",
              "2xl": "1536px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="css/styles.css" />
    <style type="text/tailwindcss">
      @layer utilities {
        .text-shadow {
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .transition-smooth {
          transition: all 0.3s ease-in-out;
        }
      }
      body.menu-open {
        overflow: hidden;
      }
      
      /* Page-specific styles */
      .report-card {
        position: relative;
        overflow: hidden;
        z-index: 1;
      }
      
      .report-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, var(--tw-gradient-from), transparent);
        opacity: 0.15;
        z-index: -1;
        transition: opacity 0.3s ease;
      }
      
      .report-card:hover::before {
        opacity: 0.25;
      }
      
      .confidential-badge {
        position: relative;
        overflow: hidden;
      }
      
      .confidential-badge::after {
        content: "";
        position: absolute;
        top: -10px;
        right: -10px;
        width: 40px;
        height: 40px;
        background-color: #DC2626;
        transform: rotate(45deg);
      }
      
      .step-connector {
        position: relative;
      }
      
      .step-connector::after {
        content: "";
        position: absolute;
        top: 36px;
        left: 18px;
        width: 2px;
        height: calc(100% - 36px);
        background-color: #E5E7EB;
      }
      
      .step-connector:last-child::after {
        display: none;
      }
    </style>
  </head>
  <body class="font-body bg-light text-dark page-transition" style="opacity: 0">
    <!-- Header/Navigation -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <div class="flex-shrink-0 flex items-center">
            <a href="/" class="flex items-center">
              <span class="font-sans font-bold text-2xl text-primary"
                >Say<span class="text-secondary">Something</span></span
              >
            </a>
          </div>

          <!-- Desktop Navigation -->
          <nav class="hidden md:flex space-x-8">
            <a
              href="index.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Home</a
            >
            <a
              href="resources.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Resources</a
            >
            <a
              href="awareness.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Awareness</a
            >
            <a
              href="report.html"
              class="font-medium text-primary border-b-2 border-primary transition-smooth"
              >Report & Support</a
            >
            <a
              href="self-care.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Self-Care</a
            >
            <a
              href="about.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >About</a
            >
            <a
              href="contact.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Contact</a
            >
          </nav>

          <!-- Mobile menu button -->
          <div class="flex md:hidden">
            <button 
              class="hamburger" 
              aria-label="Toggle mobile menu"
              aria-expanded="false"
              aria-controls="mobile-menu">
              <span></span>
              <span></span>
              <span></span>
              <span></span>
            </button>
          </div>
        </div>
      </div>

      <!-- Mobile menu container -->
      <div class="mobile-menu-container">
        <div class="flex flex-col items-center justify-center h-full">
          <a
            href="index.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Home</a
          >
          <a
            href="resources.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Resources</a
          >
          <a
            href="awareness.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Awareness</a
          >
          <a
            href="report.html"
            class="nav-item text-2xl font-medium text-primary mb-6 transition-smooth"
            >Report & Support</a
          >
          <a
            href="self-care.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Self-Care</a
          >
          <a
            href="about.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >About</a
          >
          <a
            href="contact.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary transition-smooth"
            >Contact</a
          >

          <div class="absolute bottom-10 flex space-x-4">
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                />
              </svg>
            </a>
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                />
              </svg>
            </a>
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </header>

    <!-- Main content -->
    <main>
      <!-- Hero Section with Unique Design -->
      <section class="relative py-16 md:py-24 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-red-500/5 to-purple-500/5 z-0"></div>
        
        <!-- Decorative elements -->
        <div class="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
          <div class="absolute top-20 left-20 w-64 h-64 bg-red-100 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"></div>
          <div class="absolute top-40 right-20 w-64 h-64 bg-purple-100 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000"></div>
          <div class="absolute bottom-20 left-1/3 w-64 h-64 bg-pink-100 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-4000"></div>
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div class="text-center max-w-3xl mx-auto">
            <div class="inline-flex items-center px-4 py-2 rounded-full bg-red-50 border border-red-100 mb-6">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <span class="text-sm font-medium text-red-700">Safe & Confidential Reporting</span>
            </div>
            
            <h1 class="text-4xl md:text-5xl font-sans font-bold text-dark mb-6">
              Report Sexual Harassment & <span class="text-red-600">Get Support</span>
            </h1>
            <p class="text-lg md:text-xl text-gray-600 mb-8">
              You're not alone. We provide clear paths to report incidents and connect you with the support you need.
            </p>
            
            <div class="flex flex-col sm:flex-row justify-center gap-4">
              <a href="#report-now" class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-smooth">
                Report an Incident
              </a>
              <a href="#support-options" class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md shadow-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-smooth">
                Find Support
              </a>
            </div>
          </div>
        </div>
      </section>

      <!-- What is Sexual Harassment Section -->
      <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl font-sans font-bold text-dark">What is Sexual Harassment?</h2>
            <div class="w-24 h-1 bg-red-500 mx-auto mt-4 rounded-full"></div>
          </div>

          <div class="grid md:grid-cols-2 gap-12 items-center">
            <!-- Left side - Content -->
            <div>
              <p class="text-lg text-gray-600 mb-6">
                Sexual harassment includes unwelcome sexual advances, requests for sexual favors, and other verbal or physical conduct of a sexual nature when:
              </p>
              
              <ul class="space-y-4">
                <li class="flex items-start">
                  <div class="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mt-1 mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-red-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <p class="text-gray-700"><strong>Quid Pro Quo:</strong> When submission to such conduct is made a term or condition of education or employment.</p>
                </li>
                
                <li class="flex items-start">
                  <div class="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mt-1 mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-red-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <p class="text-gray-700"><strong>Hostile Environment:</strong> When such conduct is severe, persistent, or pervasive enough to interfere with academic performance or create an intimidating environment.</p>
                </li>
                
                <li class="flex items-start">
                  <div class="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mt-1 mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-red-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <p class="text-gray-700"><strong>Gender-Based Harassment:</strong> Harassment based on gender, gender identity, or nonconformity with gender stereotypes.</p>
                </li>
              </ul>
              
              <div class="mt-8 p-4 bg-red-50 rounded-lg border border-red-100">
                <p class="text-red-800 text-sm">
                  <strong>Important:</strong> Sexual harassment can happen to anyone, regardless of gender. It can occur between people of the same or different genders, and the harasser can be a peer, faculty member, staff, or anyone else.
                </p>
              </div>
            </div>
            
            <!-- Right side - Examples -->
            <div class="bg-gray-50 rounded-xl p-6 shadow-sm">
              <h3 class="text-xl font-sans font-semibold mb-4">Examples Include:</h3>
              
              <div class="space-y-4">
                <div class="bg-white p-4 rounded-lg shadow-sm">
                  <p class="text-gray-700">Unwelcome touching, hugging, or physical contact</p>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow-sm">
                  <p class="text-gray-700">Sexual comments, jokes, gestures, or questions</p>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow-sm">
                  <p class="text-gray-700">Displaying or sharing sexually explicit materials</p>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow-sm">
                  <p class="text-gray-700">Persistent unwanted romantic attention</p>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow-sm">
                  <p class="text-gray-700">Sexual coercion or bribery</p>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow-sm">
                  <p class="text-gray-700">Cyberstalking or sending unwanted sexual content</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Reporting Options Section -->
      <section id="report-now" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl font-sans font-bold text-dark">Reporting Options</h2>
            <div class="w-24 h-1 bg-purple-500 mx-auto mt-4 rounded-full"></div>
            <p class="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              We offer multiple ways to report sexual harassment, allowing you to choose the path that feels most comfortable for you.
            </p>
          </div>

          <div class="grid md:grid-cols-3 gap-8">
            <!-- Option 1: Anonymous Reporting -->
            <div class="report-card from-red-500 bg-white rounded-xl shadow-md overflow-hidden">
              <div class="p-6">
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" stroke-dasharray="1, 3" />
                    </svg>
                  </div>
                  <h3 class="text-xl font-sans font-semibold text-dark">Anonymous Reporting</h3>
                </div>
                
                <p class="text-gray-600 mb-6">
                  Submit a report without revealing your identity. This option allows you to share information while maintaining complete confidentiality.
                </p>
                
                <div class="bg-red-50 p-4 rounded-lg mb-6">
                  <h4 class="font-medium text-red-800 mb-2">What happens next:</h4>
                  <ul class="text-sm text-red-700 space-y-2">
                    <li class="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                      <span>Your report is reviewed by trained staff</span>
                    </li>
                    <li class="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                      <span>Information is used to identify patterns and improve campus safety</span>
                    </li>
                    <li class="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                      <span>No formal investigation can be initiated without your consent</span>
                    </li>
                  </ul>
                </div>
                
                <a href="#anonymous-form" class="inline-flex items-center justify-center w-full px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-smooth">
                  File Anonymous Report
                </a>
              </div>
            </div>
            
            <!-- Option 2: Formal Complaint -->
            <div class="report-card from-purple-500 bg-white rounded-xl shadow-md overflow-hidden">
              <div class="p-6">
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 class="text-xl font-sans font-semibold text-dark">Formal Complaint</h3>
                </div>
                
                <p class="text-gray-600 mb-6">
                  File an official complaint that will be investigated by the university. This option can lead to disciplinary action against the perpetrator.
                </p>
                
                <div class="bg-purple-50 p-4 rounded-lg mb-6">
                  <h4 class="font-medium text-purple-800 mb-2">What happens next:</h4>
                  <ul class="text-sm text-purple-700 space-y-2">
                    <li class="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-600 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                      <span>A trained investigator is assigned to your case</span>
                    </li>
                    <li class="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-600 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                      <span>You'll be kept informed throughout the investigation process</span>
                    </li>
                    <li class="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-600 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                      <span>Support resources will be provided throughout the process</span>
                    </li>
                  </ul>
                </div>
                
                <a href="#formal-complaint" class="inline-flex items-center justify-center w-full px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-smooth">
                  File Formal Complaint
                </a>
              </div>
            </div>
            
            <!-- Option 3: Speak with an Advocate -->
            <div class="report-card from-blue-500 bg-white rounded-xl shadow-md overflow-hidden">
              <div class="p-6">
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                  <h3 class="text-xl font-sans font-semibold text-dark">Speak with an Advocate</h3>
                </div>
                
                <p class="text-gray-600 mb-6">
                  Talk to a trained advocate who can explain your options, provide emotional support, and help you decide what steps to take next.
                </p>
                
                <div class="bg-blue-50 p-4 rounded-lg mb-6">
                  <h4 class="font-medium text-blue-800 mb-2">What happens next:</h4>
                  <ul class="text-sm text-blue-700 space-y-2">
                    <li class="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                      <span>A confidential conversation with a trained advocate</span>
                    </li>
                    <li class="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                      <span>Guidance on available resources and reporting options</span>
                    </li>
                    <li class="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                      <span>No pressure to take any specific action</span>
                    </li>
                  </ul>
                </div>
                
                <a href="#speak-advocate" class="inline-flex items-center justify-center w-full px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-smooth">
                  Connect with an Advocate
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Support Options Section -->
      <section id="support-options" class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl font-sans font-bold text-dark">Support Resources</h2>
            <div class="w-24 h-1 bg-blue-500 mx-auto mt-4 rounded-full"></div>
            <p class="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              We're here to help you navigate this difficult time with comprehensive support services.
            </p>
          </div>

          <div class="grid md:grid-cols-2 gap-12">
            <!-- Left side - Immediate Support -->
            <div>
              <div class="bg-blue-50 rounded-xl p-6 border border-blue-100 mb-8">
                <h3 class="text-xl font-sans font-semibold text-dark mb-4 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  Immediate Support
                </h3>
                
                <div class="space-y-6">
                  <!-- Support Option 1 -->
                  <div class="bg-white rounded-lg p-4 shadow-sm">
                    <h4 class="font-medium text-dark mb-2">24/7 Crisis Hotline</h4>
                    <p class="text-gray-600 text-sm mb-3">Trained counselors available around the clock for immediate emotional support.</p>
                    <a href="tel:+233500000000" class="inline-flex items-center text-blue-600 font-medium">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                      +233 500 000 000
                    </a>
                  </div>
                  
                  <!-- Support Option 2 -->
                  <div class="bg-white rounded-lg p-4 shadow-sm">
                    <h4 class="font-medium text-dark mb-2">Campus Health Center</h4>
                    <p class="text-gray-600 text-sm mb-3">Medical care, including STI testing and emergency contraception.</p>
                    <div class="flex justify-between items-center">
                      <span class="text-sm text-gray-500">Mon-Fri: 8:00 AM - 5:00 PM</span>
                      <a href="#" class="text-blue-600 font-medium text-sm">Location Map</a>
                    </div>
                  </div>
                  
                  <!-- Support Option 3 -->
                  <div class="bg-white rounded-lg p-4 shadow-sm">
                    <h4 class="font-medium text-dark mb-2">Campus Security</h4>
                    <p class="text-gray-600 text-sm mb-3">Immediate response to ensure your physical safety on campus.</p>
                    <a href="tel:+233302123456" class="inline-flex items-center text-blue-600 font-medium">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                      +233 302 123 456
                    </a>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Right side - Ongoing Support -->
            <div>
              <div class="bg-purple-50 rounded-xl p-6 border border-purple-100">
                <h3 class="text-xl font-sans font-semibold text-dark mb-4 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                  Ongoing Support
                </h3>
                
                <div class="space-y-6">
                  <!-- Support Option 1 -->
                  <div class="bg-white rounded-lg p-4 shadow-sm">
                    <h4 class="font-medium text-dark mb-2">Counseling Services</h4>
                    <p class="text-gray-600 text-sm mb-3">Free, confidential therapy sessions with counselors specialized in trauma.</p>
                    <a href="#" class="text-purple-600 font-medium text-sm">Schedule Appointment</a>
                  </div>
                  
                  <!-- Support Option 2 -->
                  <div class="bg-white rounded-lg p-4 shadow-sm">
                    <h4 class="font-medium text-dark mb-2">Survivor Support Group</h4>
                    <p class="text-gray-600 text-sm mb-3">Weekly meetings with other survivors in a safe, facilitated environment.</p>
                    <div class="flex justify-between items-center">
                      <span class="text-sm text-gray-500">Thursdays: 6:00 PM - 7:30 PM</span>
                      <a href="#" class="text-purple-600 font-medium text-sm">Learn More</a>
                    </div>
                  </div>
                  
                  <!-- Support Option 3 -->
                  <div class="bg-white rounded-lg p-4 shadow-sm">
                    <h4 class="font-medium text-dark mb-2">Academic Accommodations</h4>
                    <p class="text-gray-600 text-sm mb-3">Support for course extensions, exam deferrals, or class changes if needed.</p>
                    <a href="#" class="text-purple-600 font-medium text-sm">Request Accommodations</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid md:grid-cols-4 gap-8">
          <!-- Logo and description -->
          <div class="col-span-1 md:col-span-2">
            <a href="/" class="flex items-center">
              <span class="font-sans font-bold text-2xl text-white"
                >Say<span class="text-secondary">Something</span></span
              >
            </a>
            <p class="mt-4 text-gray-300">
              A centralized, student-friendly platform designed to raise mental
              health awareness, guide students to relevant support systems, and
              reduce stigma on university campuses.
            </p>
          </div>

          <!-- Quick links -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="index.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Home</a
                >
              </li>
              <li>
                <a
                  href="resources.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Resources</a
                >
              </li>
              <li>
                <a
                  href="awareness.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Awareness</a
                >
              </li>
              <li>
                <a
                  href="report.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Report & Support</a
                >
              </li>
              <li>
                <a
                  href="self-care.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Self-Care</a
                >
              </li>
            </ul>
          </div>

          <!-- Contact -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Contact</h3>
            <ul class="space-y-2">
              <li class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
                <a
                  href="mailto:<EMAIL>"
                  class="text-gray-300 hover:text-white transition-smooth"
                  ><EMAIL></a
                >
              </li>
              <li class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
                <span class="text-gray-300"
                  >Takoradi Technical University, Ghana</span
                >
              </li>
            </ul>

            <div class="mt-4 flex space-x-4">
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Facebook</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                    clip-rule="evenodd"
                  />
                </svg>
              </a>
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Instagram</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                    clip-rule="evenodd"
                  />
                </svg>
              </a>
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Twitter</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                  />
                </svg>
              </a>
            </div>
          </div>
        </div>

        <div
          class="mt-12 pt-8 border-t border-gray-800 text-center text-gray-400 text-sm"
        >
          <p>Ransford Quayson | BT/ITW/24/009</p>
          <p>&copy; 2025 SaySomething. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <!-- Emergency Call Button -->
    <a href="tel:+233500000000" class="emergency-call" aria-label="Emergency call">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
      </svg>
    </a>
    
    <!-- JavaScript files -->
    <script src="js/main.js"></script>
  </body>
</html>