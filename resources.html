<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Resources - SaySomething</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#3B82F6",
              secondary: "#10B981",
              accent: "#F59E0B",
              light: "#F3F4F6",
              dark: "#1F2937",
            },
            fontFamily: {
              sans: ["Poppins", "sans-serif"],
              body: ["Lato", "sans-serif"],
            },
            screens: {
              sm: "640px",
              md: "901px",
              lg: "1024px",
              xl: "1280px",
              "2xl": "1536px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="css/styles.css" />
    <style type="text/tailwindcss">
      @layer utilities {
        .text-shadow {
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .transition-smooth {
          transition: all 0.3s ease-in-out;
        }
      }
      body.menu-open {
        overflow: hidden;
      }
    </style>
  </head>
  <body class="font-body bg-light text-dark page-transition" style="opacity: 0">
    <!-- Header/Navigation -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <div class="flex-shrink-0 flex items-center">
            <a href="/" class="flex items-center">
              <span class="font-sans font-bold text-2xl text-primary"
                >Say<span class="text-secondary">Something</span></span
              >
            </a>
          </div>

          <!-- Desktop Navigation -->
          <nav class="hidden md:flex space-x-8">
            <a
              href="index.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Home</a
            >
            <a
              href="resources.html"
              class="font-medium text-primary border-b-2 border-primary transition-smooth"
              >Resources</a
            >
            <a
              href="awareness.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Awareness</a
            >
            <a
              href="report.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Report & Support</a
            >
            <a
              href="self-care.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Self-Care</a
            >
            <a
              href="about.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >About</a
            >
            <a
              href="contact.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Contact</a
            >
          </nav>

          <!-- Mobile menu button -->
          <div class="flex md:hidden">
            <button
              class="hamburger"
              aria-label="Toggle mobile menu"
              aria-expanded="false"
              aria-controls="mobile-menu"
            >
              <span></span>
              <span></span>
              <span></span>
              <span></span>
            </button>
          </div>
        </div>
      </div>

      <!-- Mobile menu container -->
      <div class="mobile-menu-container">
        <div class="flex flex-col items-center justify-center h-full">
          <a
            href="index.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Home</a
          >
          <a
            href="resources.html"
            class="nav-item text-2xl font-medium text-primary mb-6 transition-smooth"
            >Resources</a
          >
          <a
            href="awareness.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Awareness</a
          >
          <a
            href="report.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Report & Support</a
          >
          <a
            href="self-care.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Self-Care</a
          >
          <a
            href="about.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >About</a
          >
          <a
            href="contact.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary transition-smooth"
            >Contact</a
          >

          <div class="absolute bottom-10 flex space-x-4">
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                />
              </svg>
            </a>
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                />
              </svg>
            </a>
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </header>

    <!-- Main content -->
    <main>
      <!-- Hero Section -->
      <section
        class="bg-gradient-to-br from-primary/5 to-secondary/5 py-16 md:py-24"
      >
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center max-w-3xl mx-auto">
            <h1 class="text-4xl md:text-5xl font-sans font-bold text-dark mb-6">
              Mental Health <span class="gradient-text">Resources</span>
            </h1>
            <p class="text-lg md:text-xl text-gray-600 mb-8">
              A comprehensive directory of support services, tools, and
              communities to help you navigate your mental health journey.
            </p>
          </div>
        </div>
      </section>

      <!-- Search & Filter Section -->
      <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="bg-white rounded-xl shadow-md p-6">
            <div class="grid md:grid-cols-3 gap-4">
              <!-- Search input -->
              <div class="relative">
                <div
                  class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                  <svg
                    class="h-5 w-5 text-gray-400"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
                <input
                  type="text"
                  class="pl-10 w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20"
                  placeholder="Search resources..."
                />
              </div>

              <!-- Resource type filter -->
              <div>
                <select
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20"
                >
                  <option value="">All Resource Types</option>
                  <option value="counseling">Counseling Services</option>
                  <option value="peer-support">Peer Support Groups</option>
                  <option value="crisis">Crisis Support</option>
                  <option value="off-campus">Off-Campus Partners</option>
                  <option value="academic">Academic Adjustments</option>
                </select>
              </div>

              <!-- Location filter -->
              <div>
                <select
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20"
                >
                  <option value="">All Locations</option>
                  <option value="on-campus">On Campus</option>
                  <option value="off-campus">Off Campus</option>
                  <option value="online">Online/Remote</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Counseling Services Section -->
      <section class="py-16 bg-light">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-sans font-bold text-dark">
              Counseling Services
            </h2>
            <div class="w-24 h-1 bg-primary mx-auto mt-4 rounded-full"></div>
            <p class="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              Professional support for students facing mental health challenges.
            </p>
          </div>

          <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Counseling Service 1 -->
            <div
              class="bg-white rounded-xl shadow-sm hover:shadow-md transition-smooth p-6"
            >
              <div class="flex items-center mb-4">
                <div
                  class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mr-4"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                </div>
                <h3 class="text-xl font-sans font-semibold text-dark">
                  TTU Counseling Center
                </h3>
              </div>
              <p class="text-gray-600 mb-4">
                Free, confidential counseling services for all enrolled
                students. Individual and group sessions available.
              </p>
              <div class="space-y-2 text-sm">
                <div class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-500 mr-2 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  <span>Student Affairs Building, 2nd Floor</span>
                </div>
                <div class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-500 mr-2 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span>Mon-Fri: 8:00 AM - 5:00 PM</span>
                </div>
                <div class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-500 mr-2 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    />
                  </svg>
                  <span>+233 302 123 4567</span>
                </div>
              </div>
              <div class="mt-6">
                <a
                  href="#"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-smooth"
                >
                  Schedule Appointment
                </a>
              </div>
            </div>

            <!-- Counseling Service 2 -->
            <div
              class="bg-white rounded-xl shadow-sm hover:shadow-md transition-smooth p-6"
            >
              <div class="flex items-center mb-4">
                <div
                  class="w-12 h-12 bg-secondary/10 rounded-full flex items-center justify-center mr-4"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-secondary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                </div>
                <h3 class="text-xl font-sans font-semibold text-dark">
                  Group Therapy Sessions
                </h3>
              </div>
              <p class="text-gray-600 mb-4">
                Weekly group sessions focusing on anxiety, depression, grief,
                and stress management. Led by licensed therapists.
              </p>
              <div class="space-y-2 text-sm">
                <div class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-500 mr-2 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  <span>Wellness Center, Room 105</span>
                </div>
                <div class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-500 mr-2 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span>Various times - See schedule</span>
                </div>
                <div class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-500 mr-2 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  <span><EMAIL></span>
                </div>
              </div>
              <div class="mt-6">
                <a
                  href="#"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-secondary hover:bg-secondary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary transition-smooth"
                >
                  View Schedule
                </a>
              </div>
            </div>

            <!-- Counseling Service 3 -->
            <div
              class="bg-white rounded-xl shadow-sm hover:shadow-md transition-smooth p-6"
            >
              <div class="flex items-center mb-4">
                <div
                  class="w-12 h-12 bg-accent/10 rounded-full flex items-center justify-center mr-4"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-accent"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <h3 class="text-xl font-sans font-semibold text-dark">
                  Virtual Counseling
                </h3>
              </div>
              <p class="text-gray-600 mb-4">
                Secure online counseling sessions available for students who
                prefer remote support or are off-campus.
              </p>
              <div class="space-y-2 text-sm">
                <div class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-500 mr-2 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"
                    />
                  </svg>
                  <span>Online via secure platform</span>
                </div>
                <div class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-500 mr-2 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span>Flexible scheduling available</span>
                </div>
                <div class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-500 mr-2 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  <span><EMAIL></span>
                </div>
              </div>
              <div class="mt-6">
                <a
                  href="#"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-accent hover:bg-accent/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent transition-smooth"
                >
                  Book Online Session
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Crisis Support Section -->
      <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-sans font-bold text-dark">
              Crisis Support
            </h2>
            <div class="w-24 h-1 bg-accent mx-auto mt-4 rounded-full"></div>
            <p class="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              Immediate help for urgent mental health situations.
            </p>
          </div>

          <div
            class="bg-gradient-to-br from-accent/5 to-primary/5 rounded-2xl p-8 shadow-sm"
          >
            <div class="grid md:grid-cols-2 gap-8">
              <!-- Left side - Emergency contacts -->
              <div>
                <h3 class="text-2xl font-sans font-semibold text-dark mb-6">
                  24/7 Emergency Contacts
                </h3>

                <div class="space-y-6">
                  <!-- Emergency Contact 1 -->
                  <div
                    class="flex items-start bg-white rounded-xl p-4 shadow-sm"
                  >
                    <div
                      class="flex-shrink-0 w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-6 w-6 text-red-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                        />
                      </svg>
                    </div>
                    <div>
                      <h4 class="text-lg font-medium text-dark">
                        National Crisis Hotline
                      </h4>
                      <p class="text-gray-600 text-sm mb-2">
                        Immediate support for suicidal thoughts or severe
                        distress
                      </p>
                      <a
                        href="tel:+233500000000"
                        class="text-red-600 font-medium flex items-center"
                      >
                        <span>+233 500 000 000</span>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4 ml-1"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                          />
                        </svg>
                      </a>
                    </div>
                  </div>

                  <!-- Emergency Contact 2 -->
                  <div
                    class="flex items-start bg-white rounded-xl p-4 shadow-sm"
                  >
                    <div
                      class="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mr-4"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-6 w-6 text-primary"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    </div>
                    <div>
                      <h4 class="text-lg font-medium text-dark">
                        Campus Security
                      </h4>
                      <p class="text-gray-600 text-sm mb-2">
                        On-campus emergency response team
                      </p>
                      <a
                        href="tel:+233302123456"
                        class="text-primary font-medium flex items-center"
                      >
                        <span>+233 302 123 456</span>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4 ml-1"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                          />
                        </svg>
                      </a>
                    </div>
                  </div>

                  <!-- Emergency Contact 3 -->
                  <div
                    class="flex items-start bg-white rounded-xl p-4 shadow-sm"
                  >
                    <div
                      class="flex-shrink-0 w-12 h-12 bg-secondary/10 rounded-full flex items-center justify-center mr-4"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-6 w-6 text-secondary"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                        />
                      </svg>
                    </div>
                    <div>
                      <h4 class="text-lg font-medium text-dark">
                        Hospital Emergency
                      </h4>
                      <p class="text-gray-600 text-sm mb-2">
                        TTU Medical Center emergency services
                      </p>
                      <a
                        href="tel:+233302789012"
                        class="text-secondary font-medium flex items-center"
                      >
                        <span>+233 302 789 012</span>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4 ml-1"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                          />
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Right side - What constitutes a crisis -->
              <div>
                <h3 class="text-2xl font-sans font-semibold text-dark mb-6">
                  When to Seek Crisis Support
                </h3>

                <div class="bg-white rounded-xl p-6 shadow-sm">
                  <ul class="space-y-4">
                    <li class="flex items-start">
                      <div
                        class="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mt-0.5 mr-3"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4 text-red-600"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                      <div>
                        <h4 class="font-medium text-dark">
                          Suicidal thoughts or behaviors
                        </h4>
                        <p class="text-gray-600 text-sm">
                          Thoughts of harming yourself or ending your life
                        </p>
                      </div>
                    </li>

                    <li class="flex items-start">
                      <div
                        class="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mt-0.5 mr-3"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4 text-red-600"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                      <div>
                        <h4 class="font-medium text-dark">
                          Severe panic attacks
                        </h4>
                        <p class="text-gray-600 text-sm">
                          Overwhelming anxiety that interferes with daily
                          functioning
                        </p>
                      </div>
                    </li>

                    <li class="flex items-start">
                      <div
                        class="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mt-0.5 mr-3"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4 text-red-600"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                      <div>
                        <h4 class="font-medium text-dark">Self-harm</h4>
                        <p class="text-gray-600 text-sm">
                          Engaging in behaviors that cause physical harm to
                          yourself
                        </p>
                      </div>
                    </li>

                    <li class="flex items-start">
                      <div
                        class="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mt-0.5 mr-3"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4 text-red-600"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                      <div>
                        <h4 class="font-medium text-dark">Severe depression</h4>
                        <p class="text-gray-600 text-sm">
                          Unable to function, care for yourself, or get out of
                          bed
                        </p>
                      </div>
                    </li>

                    <li class="flex items-start">
                      <div
                        class="flex-shrink-0 w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mt-0.5 mr-3"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4 text-red-600"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                      <div>
                        <h4 class="font-medium text-dark">Psychosis</h4>
                        <p class="text-gray-600 text-sm">
                          Experiencing hallucinations, delusions, or severe
                          disorganized thinking
                        </p>
                      </div>
                    </li>
                  </ul>

                  <div
                    class="mt-6 p-4 bg-red-50 rounded-lg border border-red-100"
                  >
                    <p class="text-red-800 text-sm font-medium">
                      If you or someone you know is experiencing any of these
                      symptoms, please reach out to one of the crisis resources
                      immediately. Don't wait.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Peer Support Section -->
      <section class="py-16 bg-light">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-sans font-bold text-dark">
              Peer Support Groups
            </h2>
            <div class="w-24 h-1 bg-secondary mx-auto mt-4 rounded-full"></div>
            <p class="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              Connect with fellow students who understand what you're going
              through.
            </p>
          </div>

          <div class="grid md:grid-cols-2 gap-8">
            <!-- Support Group 1 -->
            <div
              class="bg-white rounded-xl shadow-sm hover:shadow-md transition-smooth p-6"
            >
              <div class="flex items-center mb-4">
                <div
                  class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mr-4"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                    />
                  </svg>
                </div>
                <h3 class="text-xl font-sans font-semibold text-dark">
                  Anxiety & Stress Support Circle
                </h3>
              </div>
              <p class="text-gray-600 mb-4">
                A safe space for students dealing with anxiety, stress, and
                overwhelming academic pressure. Share experiences and coping
                strategies.
              </p>
              <div class="space-y-2 text-sm mb-6">
                <div class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-500 mr-2 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span>Tuesdays, 5:00 PM - 6:30 PM</span>
                </div>
                <div class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-500 mr-2 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  <span>Student Center, Room 203</span>
                </div>
                <div class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-500 mr-2 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                  <span>Facilitated by Sarah Mensah, Peer Counselor</span>
                </div>
              </div>
              <div class="flex justify-between">
                <a
                  href="#"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-smooth"
                >
                  Join Group
                </a>
                <span
                  class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-primary/10 text-primary"
                >
                  15 members
                </span>
              </div>
            </div>

            <!-- Support Group 2 -->
            <div
              class="bg-white rounded-xl shadow-sm hover:shadow-md transition-smooth p-6"
            >
              <div class="flex items-center mb-4">
                <div
                  class="w-12 h-12 bg-secondary/10 rounded-full flex items-center justify-center mr-4"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-secondary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                    />
                  </svg>
                </div>
                <h3 class="text-xl font-sans font-semibold text-dark">
                  Mindfulness & Self-Care Group
                </h3>
              </div>
              <p class="text-gray-600 mb-4">
                Learn and practice mindfulness techniques, meditation, and
                self-care strategies to improve mental wellbeing and resilience.
              </p>
              <div class="space-y-2 text-sm mb-6">
                <div class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-500 mr-2 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span>Thursdays, 4:00 PM - 5:30 PM</span>
                </div>
                <div class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-500 mr-2 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  <span>Wellness Center, Meditation Room</span>
                </div>
                <div class="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-500 mr-2 mt-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                  <span>Facilitated by David Owusu, Wellness Coach</span>
                </div>
              </div>
              <div class="flex justify-between">
                <a
                  href="#"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-secondary hover:bg-secondary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary transition-smooth"
                >
                  Join Group
                </a>
                <span
                  class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-secondary/10 text-secondary"
                >
                  22 members
                </span>
              </div>
            </div>
          </div>

          <div class="mt-12 text-center">
            <a
              href="#"
              class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-secondary hover:bg-secondary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary transition-smooth"
            >
              View All Support Groups
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 ml-2"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                />
              </svg>
            </a>
          </div>
        </div>
      </section>
    </main>

    <!-- Resource Submission Section -->
    <section class="py-16 bg-white border-t border-gray-100">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid md:grid-cols-2 gap-12 items-center">
          <!-- Left side - Form -->
          <div>
            <h2 class="text-2xl font-sans font-bold text-dark mb-6">
              Suggest a Resource
            </h2>
            <p class="text-gray-600 mb-8">
              Know of a mental health resource that could benefit fellow
              students? Share it with us and help expand our directory.
            </p>

            <form
              id="resources-form"
              action="/api/resources"
              method="POST"
              class="space-y-6"
            >
              <div>
                <label
                  for="resource-name"
                  class="block text-sm font-medium text-gray-700 mb-1"
                  >Resource Name</label
                >
                <input
                  type="text"
                  id="resource-name"
                  name="resource-name"
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20"
                  placeholder="Name of organization, service, or tool"
                  required
                />
              </div>

              <div>
                <label
                  for="resource-type"
                  class="block text-sm font-medium text-gray-700 mb-1"
                  >Resource Type</label
                >
                <select
                  id="resource-type"
                  name="resource-type"
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20"
                  required
                >
                  <option value="">Select type...</option>
                  <option value="counseling">Counseling Service</option>
                  <option value="peer-support">Peer Support Group</option>
                  <option value="crisis">Crisis Support</option>
                  <option value="off-campus">Off-Campus Partner</option>
                  <option value="academic">Academic Support</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label
                  for="resource-website"
                  class="block text-sm font-medium text-gray-700 mb-1"
                  >Website (Optional)</label
                >
                <input
                  type="url"
                  id="resource-website"
                  name="resource-website"
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20"
                  placeholder="https://example.com"
                />
              </div>

              <div>
                <label
                  for="resource-contact"
                  class="block text-sm font-medium text-gray-700 mb-1"
                  >Contact Information (Optional)</label
                >
                <input
                  type="text"
                  id="resource-contact"
                  name="resource-contact"
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20"
                  placeholder="Phone, email, or other contact info"
                />
              </div>

              <div>
                <label
                  for="resource-description"
                  class="block text-sm font-medium text-gray-700 mb-1"
                  >Description</label
                >
                <textarea
                  id="resource-description"
                  name="resource-description"
                  rows="4"
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20"
                  placeholder="Please describe what this resource offers and how it can help students"
                  required
                ></textarea>
              </div>

              <div>
                <label
                  for="submitter-name"
                  class="block text-sm font-medium text-gray-700 mb-1"
                  >Your Name (Optional)</label
                >
                <input
                  type="text"
                  id="submitter-name"
                  name="submitter-name"
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20"
                  placeholder="Your name"
                />
              </div>

              <div>
                <label
                  for="submitter-email"
                  class="block text-sm font-medium text-gray-700 mb-1"
                  >Your Email (Optional)</label
                >
                <input
                  type="email"
                  id="submitter-email"
                  name="submitter-email"
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-20"
                  placeholder="Your email address"
                />
              </div>

              <div>
                <button
                  type="submit"
                  class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-smooth"
                >
                  Submit Resource
                </button>
              </div>
            </form>
          </div>

          <!-- Right side - Image & text -->
          <div class="relative hidden md:block">
            <div
              class="absolute -top-6 -right-6 w-24 h-24 bg-accent/20 rounded-full blur-2xl"
            ></div>
            <div
              class="absolute -bottom-8 -left-8 w-32 h-32 bg-primary/20 rounded-full blur-2xl"
            ></div>

            <div
              class="relative bg-white rounded-2xl shadow-xl overflow-hidden p-1 img-hover-zoom"
            >
              <div class="aspect-w-4 aspect-h-3 rounded-xl overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1573497620053-ea5300f94f21?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Students collaborating"
                  class="w-full h-full object-cover"
                />
              </div>
            </div>

            <div
              class="mt-8 bg-white p-6 rounded-xl shadow-sm border border-gray-100"
            >
              <h3 class="text-lg font-medium text-dark mb-2">
                Community-Driven Support
              </h3>
              <p class="text-gray-600">
                Our resource directory grows stronger with each student
                contribution. By sharing resources that have helped you, you're
                extending a helping hand to others who may be struggling.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid md:grid-cols-4 gap-8">
          <!-- Logo and description -->
          <div class="col-span-1 md:col-span-2">
            <a href="/" class="flex items-center">
              <span class="font-sans font-bold text-2xl text-white"
                >Say<span class="text-secondary">Something</span></span
              >
            </a>
            <p class="mt-4 text-gray-300">
              A centralized, student-friendly platform designed to raise mental
              health awareness, guide students to relevant support systems, and
              reduce stigma on university campuses.
            </p>
          </div>

          <!-- Quick links -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="index.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Home</a
                >
              </li>
              <li>
                <a
                  href="resources.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Resources</a
                >
              </li>
              <li>
                <a
                  href="awareness.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Awareness</a
                >
              </li>
              <li>
                <a
                  href="report.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Report & Support</a
                >
              </li>
              <li>
                <a
                  href="self-care.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Self-Care</a
                >
              </li>
            </ul>
          </div>

          <!-- Contact -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Contact</h3>
            <ul class="space-y-2">
              <li class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
                <a
                  href="mailto:<EMAIL>"
                  class="text-gray-300 hover:text-white transition-smooth"
                  ><EMAIL></a
                >
              </li>
              <li class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
                <span class="text-gray-300"
                  >Takoradi Technical University, Ghana</span
                >
              </li>
            </ul>

            <div class="mt-4 flex space-x-4">
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Facebook</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                    clip-rule="evenodd"
                  />
                </svg>
              </a>
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Instagram</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                    clip-rule="evenodd"
                  />
                </svg>
              </a>
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Twitter</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                  />
                </svg>
              </a>
            </div>
          </div>
        </div>

        <div
          class="mt-12 pt-8 border-t border-gray-800 text-center text-gray-400 text-sm"
        >
          <p>Ransford Quayson | BT/ITW/24/009</p>
          <p>&copy; 2025 SaySomething. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <!-- Emergency Call Button -->
    <a
      href="tel:+233500000000"
      class="emergency-call"
      aria-label="Emergency call"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
        />
      </svg>
    </a>

    <!-- JavaScript files -->
    <script src="js/main.js"></script>
    <script src="js/forms.js"></script>
  </body>
</html>
