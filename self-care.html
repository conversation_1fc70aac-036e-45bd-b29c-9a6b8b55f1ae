<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Self-Care - SaySomething</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#3B82F6",
              secondary: "#10B981",
              accent: "#F59E0B",
              light: "#F3F4F6",
              dark: "#1F2937",
            },
            fontFamily: {
              sans: ["Poppins", "sans-serif"],
              body: ["Lato", "sans-serif"],
            },
            screens: {
              sm: "640px",
              md: "901px",
              lg: "1024px",
              xl: "1280px",
              "2xl": "1536px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="css/styles.css" />
    <style type="text/tailwindcss">
      @layer utilities {
        .text-shadow {
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .transition-smooth {
          transition: all 0.3s ease-in-out;
        }
      }
      body.menu-open {
        overflow: hidden;
      }
    </style>
  </head>
  <body class="font-body bg-light text-dark page-transition" style="opacity: 0">
    <!-- Header/Navigation -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <div class="flex-shrink-0 flex items-center">
            <a href="/" class="flex items-center">
              <span class="font-sans font-bold text-2xl text-primary"
                >Say<span class="text-secondary">Something</span></span
              >
            </a>
          </div>

          <!-- Desktop Navigation -->
          <nav class="hidden md:flex space-x-8">
            <a
              href="index.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Home</a
            >
            <a
              href="resources.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Resources</a
            >
            <a
              href="awareness.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Awareness</a
            >
            <a
              href="report.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Report & Support</a
            >
            <a
              href="self-care.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Self-Care</a
            >
            <a
              href="about.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >About</a
            >
            <a
              href="contact.html"
              class="font-medium text-gray-600 hover:text-primary transition-smooth"
              >Contact</a
            >
          </nav>

          <!-- Mobile menu button -->
          <div class="flex md:hidden">
            <button 
              class="hamburger" 
              aria-label="Toggle mobile menu"
              aria-expanded="false"
              aria-controls="mobile-menu">
              <span></span>
              <span></span>
              <span></span>
              <span></span>
            </button>
          </div>
        </div>
      </div>

      <!-- Mobile menu container -->
      <div class="mobile-menu-container">
        <div class="flex flex-col items-center justify-center h-full">
          <a
            href="index.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Home</a
          >
          <a
            href="resources.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Resources</a
          >
          <a
            href="awareness.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Awareness</a
          >
          <a
            href="report.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Report & Support</a
          >
          <a
            href="self-care.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >Self-Care</a
          >
          <a
            href="about.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary mb-6 transition-smooth"
            >About</a
          >
          <a
            href="contact.html"
            class="nav-item text-2xl font-medium text-dark hover:text-primary transition-smooth"
            >Contact</a
          >

          <div class="absolute bottom-10 flex space-x-4">
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                />
              </svg>
            </a>
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                />
              </svg>
            </a>
            <a
              href="#"
              class="text-gray-500 hover:text-primary transition-smooth"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </header>

    <main>
      <!-- Hero Section -->
      <section class="relative bg-gradient-to-br from-secondary/10 to-accent/10 overflow-hidden">
        <!-- Background pattern -->
        <div class="absolute inset-0 opacity-10">
          <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="grid" width="8" height="8" patternUnits="userSpaceOnUse">
                <path d="M0 8L8 0" stroke="currentColor" stroke-width="0.5" fill="none" />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
          </svg>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32 relative">
          <div class="grid md:grid-cols-2 gap-12 items-center">
            <!-- Left content -->
            <div class="space-y-8 animate-fade-in">
              <div class="space-y-4">
                <h1 class="font-sans font-bold text-4xl md:text-5xl lg:text-6xl text-dark text-shadow leading-tight">
                  Self-Care <span class="text-secondary">Matters</span>
                </h1>
                <p class="text-lg md:text-xl text-gray-600 max-w-lg">
                  Discover practical techniques and resources to nurture your mental wellbeing and build resilience in your daily life.
                </p>
              </div>

              <div class="flex flex-wrap gap-4">
                <a href="#stress-management" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-secondary hover:bg-secondary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary transition-smooth hover-lift">
                  Explore Techniques
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </a>
                <a href="#self-care-routine" class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent transition-smooth hover-lift">
                  Build Your Routine
                </a>
              </div>
            </div>

            <!-- Right content - Illustration -->
            <div class="relative hidden md:block animate-fade-in" style="animation-delay: 0.3s">
              <div class="absolute -top-6 -right-6 w-24 h-24 bg-accent/20 rounded-full blur-2xl"></div>
              <div class="absolute -bottom-8 -left-8 w-32 h-32 bg-secondary/20 rounded-full blur-2xl"></div>

              <div class="relative bg-white rounded-2xl shadow-xl overflow-hidden p-1 img-hover-zoom">
                <div class="aspect-w-4 aspect-h-3 rounded-xl overflow-hidden">
                  <img src="https://images.unsplash.com/photo-1545205597-3d9d02c29597?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Person practicing mindfulness outdoors" class="w-full h-full object-cover" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Stress Management Section -->
      <section id="stress-management" class="py-16 md:py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16 animate-fade-in">
            <h2 class="text-3xl md:text-4xl font-sans font-bold text-dark">
              Stress Management Techniques
            </h2>
            <div class="w-24 h-1 bg-secondary mx-auto mt-4 rounded-full"></div>
            <p class="mt-6 text-lg text-gray-600 max-w-3xl mx-auto">
              Discover effective ways to manage stress and anxiety in your daily student life.
            </p>
          </div>

          <div class="grid md:grid-cols-3 gap-8 md:gap-12 stagger-children">
            <!-- Technique 1: Deep Breathing -->
            <div class="bg-light rounded-xl p-8 shadow-sm hover:shadow-md transition-smooth hover-lift">
              <div class="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4" />
                </svg>
              </div>
              <h3 class="text-xl font-sans font-semibold mb-3">
                Deep Breathing
              </h3>
              <p class="text-gray-600 mb-4">
                When stress hits, your breathing becomes shallow. Counteract this with deep, diaphragmatic breathing to activate your body's relaxation response.
              </p>
              <div class="bg-white rounded-lg p-4 border border-gray-100">
                <h4 class="font-medium text-gray-800 mb-2">Try This:</h4>
                <ol class="text-gray-600 space-y-2 list-decimal list-inside">
                  <li>Find a quiet place to sit or lie down</li>
                  <li>Place one hand on your chest and one on your stomach</li>
                  <li>Breathe in slowly through your nose for 4 counts</li>
                  <li>Hold for 2 counts</li>
                  <li>Exhale slowly through your mouth for 6 counts</li>
                  <li>Repeat for 5-10 minutes</li>
                </ol>
              </div>
            </div>

            <!-- Technique 2: Progressive Muscle Relaxation -->
            <div class="bg-light rounded-xl p-8 shadow-sm hover:shadow-md transition-smooth hover-lift">
              <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 class="text-xl font-sans font-semibold mb-3">
                Progressive Muscle Relaxation
              </h3>
              <p class="text-gray-600 mb-4">
                This technique involves tensing and then releasing each muscle group in your body to reduce physical tension and mental stress.
              </p>
              <div class="bg-white rounded-lg p-4 border border-gray-100">
                <h4 class="font-medium text-gray-800 mb-2">Try This:</h4>
                <ol class="text-gray-600 space-y-2 list-decimal list-inside">
                  <li>Start with your feet and work upward</li>
                  <li>Tense each muscle group for 5 seconds</li>
                  <li>Release and notice the feeling of relaxation</li>
                  <li>Move to the next muscle group</li>
                  <li>Continue until you've relaxed your entire body</li>
                </ol>
              </div>
            </div>

            <!-- Technique 3: Mindful Grounding -->
            <div class="bg-light rounded-xl p-8 shadow-sm hover:shadow-md transition-smooth hover-lift">
              <div class="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 class="text-xl font-sans font-semibold mb-3">
                5-4-3-2-1 Grounding
              </h3>
              <p class="text-gray-600 mb-4">
                When anxiety takes over, this sensory awareness exercise can help bring you back to the present moment.
              </p>
              <div class="bg-white rounded-lg p-4 border border-gray-100">
                <h4 class="font-medium text-gray-800 mb-2">Try This:</h4>
                <ul class="text-gray-600 space-y-2">
                  <li><span class="font-medium text-accent">5:</span> Name 5 things you can see</li>
                  <li><span class="font-medium text-accent">4:</span> Name 4 things you can touch</li>
                  <li><span class="font-medium text-accent">3:</span> Name 3 things you can hear</li>
                  <li><span class="font-medium text-accent">2:</span> Name 2 things you can smell</li>
                  <li><span class="font-medium text-accent">1:</span> Name 1 thing you can taste</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Sleep Improvement Section -->
      <section class="py-16 md:py-24 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16 animate-fade-in">
            <h2 class="text-3xl md:text-4xl font-sans font-bold text-dark">
              Improving Sleep Quality
            </h2>
            <div class="w-24 h-1 bg-primary mx-auto mt-4 rounded-full"></div>
            <p class="mt-6 text-lg text-gray-600 max-w-3xl mx-auto">
              Quality sleep is essential for mental health and academic performance. Here's how to improve your sleep hygiene.
            </p>
          </div>

          <div class="grid md:grid-cols-2 gap-8 lg:gap-16 items-center">
            <!-- Left side - Image -->
            <div class="relative">
              <div class="absolute -top-6 -left-6 w-24 h-24 bg-primary/20 rounded-full blur-2xl"></div>
              <div class="absolute -bottom-8 -right-8 w-32 h-32 bg-secondary/20 rounded-full blur-2xl"></div>

              <div class="relative bg-white rounded-2xl shadow-xl overflow-hidden p-1 img-hover-zoom">
                <div class="aspect-w-4 aspect-h-3 rounded-xl overflow-hidden">
                  <img src="https://images.unsplash.com/photo-1541781774459-bb2af2f05b55?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Peaceful bedroom setting" class="w-full h-full object-cover" />
                </div>
              </div>
            </div>

            <!-- Right side - Content -->
            <div class="space-y-6">
              <h3 class="text-2xl font-sans font-semibold text-dark">
                Sleep Hygiene Practices
              </h3>

              <div class="space-y-4">
                <!-- Sleep tip 1 -->
                <div class="flex items-start gap-3">
                  <div class="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800">Consistent Schedule</h4>
                    <p class="text-gray-600">Go to bed and wake up at the same time every day, even on weekends. This helps regulate your body's internal clock.</p>
                  </div>
                </div>

                <!-- Sleep tip 2 -->
                <div class="flex items-start gap-3">
                  <div class="flex-shrink-0 w-6 h-6 bg-secondary rounded-full flex items-center justify-center mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800">Screen-Free Time</h4>
                    <p class="text-gray-600">Avoid screens (phones, computers, TV) for at least 30-60 minutes before bedtime. The blue light can interfere with melatonin production.</p>
                  </div>
                </div>

                <!-- Sleep tip 3 -->
                <div class="flex items-start gap-3">
                  <div class="flex-shrink-0 w-6 h-6 bg-accent rounded-full flex items-center justify-center mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800">Comfortable Environment</h4>
                    <p class="text-gray-600">Keep your bedroom cool, dark, and quiet. Consider using earplugs, an eye mask, or a white noise machine if needed.</p>
                  </div>
                </div>

                <!-- Sleep tip 4 -->
                <div class="flex items-start gap-3">
                  <div class="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800">Limit Caffeine and Alcohol</h4>
                    <p class="text-gray-600">Avoid caffeine after 2pm and limit alcohol, which can disrupt your sleep cycle and reduce sleep quality.</p>
                  </div>
                </div>

                <!-- Sleep tip 5 -->
                <div class="flex items-start gap-3">
                  <div class="flex-shrink-0 w-6 h-6 bg-secondary rounded-full flex items-center justify-center mt-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800">Relaxation Routine</h4>
                    <p class="text-gray-600">Develop a pre-sleep ritual like reading, gentle stretching, or meditation to signal to your body that it's time to wind down.</p>
                  </div>
                </div>
              </div>

              <div class="pt-4">
                <a href="#" class="text-primary hover:text-primary/80 font-medium flex items-center">
                  Download Sleep Tracker Template
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Mindfulness Practices Section -->
      <section id="mindfulness" class="py-16 md:py-24 bg-white overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <!-- Background decoration -->
          <div class="absolute top-1/4 right-0 w-64 h-64 bg-primary/5 rounded-full blur-3xl -z-10"></div>
          <div class="absolute bottom-1/4 left-0 w-64 h-64 bg-secondary/5 rounded-full blur-3xl -z-10"></div>

          <div class="text-center mb-16 animate-fade-in">
            <h2 class="text-3xl md:text-4xl font-sans font-bold text-dark">
              Mindfulness Practices
            </h2>
            <div class="w-24 h-1 bg-accent mx-auto mt-4 rounded-full"></div>
            <p class="mt-6 text-lg text-gray-600 max-w-3xl mx-auto">
              Mindfulness helps you stay present, reduce rumination, and improve focus. Try these simple practices in your daily life.
            </p>
          </div>

          <div class="grid md:grid-cols-2 gap-12 lg:gap-16 items-center stagger-children">
            <!-- Left side - Content -->
            <div class="space-y-8">
              <div class="bg-light rounded-xl p-6 shadow-sm">
                <h3 class="text-xl font-sans font-semibold mb-3 flex items-center">
                  <span class="w-8 h-8 bg-accent/20 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <span class="text-accent font-bold">1</span>
                  </span>
                  Mindful Breathing
                </h3>
                <p class="text-gray-600">
                  Focus your attention on your breath. Notice the sensation of air flowing in and out of your body. When your mind wanders, gently bring your attention back to your breath.
                </p>
                <div class="mt-3 text-sm text-accent font-medium">
                  Try for: 5 minutes daily
                </div>
              </div>

              <div class="bg-light rounded-xl p-6 shadow-sm">
                <h3 class="text-xl font-sans font-semibold mb-3 flex items-center">
                  <span class="w-8 h-8 bg-accent/20 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <span class="text-accent font-bold">2</span>
                  </span>
                  Body Scan
                </h3>
                <p class="text-gray-600">
                  Lie down or sit comfortably. Slowly bring attention to each part of your body, from your toes to the top of your head. Notice any sensations without judgment.
                </p>
                <div class="mt-3 text-sm text-accent font-medium">
                  Try for: 10-15 minutes before sleep
                </div>
              </div>

              <div class="bg-light rounded-xl p-6 shadow-sm">
                <h3 class="text-xl font-sans font-semibold mb-3 flex items-center">
                  <span class="w-8 h-8 bg-accent/20 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <span class="text-accent font-bold">3</span>
                  </span>
                  Mindful Walking
                </h3>
                <p class="text-gray-600">
                  As you walk, pay attention to each step. Feel your feet touching the ground, notice the movement of your body, and observe your surroundings with full awareness.
                </p>
                <div class="mt-3 text-sm text-accent font-medium">
                  Try for: 10 minutes between classes
                </div>
              </div>

              <div class="bg-light rounded-xl p-6 shadow-sm">
                <h3 class="text-xl font-sans font-semibold mb-3 flex items-center">
                  <span class="w-8 h-8 bg-accent/20 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <span class="text-accent font-bold">4</span>
                  </span>
                  Mindful Eating
                </h3>
                <p class="text-gray-600">
                  Eat without distractions. Notice the colors, smells, textures, and flavors of your food. Chew slowly and savor each bite with full attention.
                </p>
                <div class="mt-3 text-sm text-accent font-medium">
                  Try for: At least one meal per day
                </div>
              </div>
            </div>

            <!-- Right side - Image -->
            <div class="relative order-first md:order-last animate-fade-in">
              <div class="absolute -top-6 -right-6 w-24 h-24 bg-accent/20 rounded-full blur-2xl"></div>
              <div class="absolute -bottom-8 -left-8 w-32 h-32 bg-primary/20 rounded-full blur-2xl"></div>

              <div class="relative bg-white rounded-2xl shadow-xl overflow-hidden p-1 img-hover-zoom">
                <div class="aspect-w-1 aspect-h-1 rounded-xl overflow-hidden">
                  <img src="https://images.unsplash.com/photo-1506126613408-eca07ce68773?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Person meditating outdoors" class="w-full h-full object-cover" />
                </div>
              </div>

              <div class="mt-6 bg-white rounded-xl p-6 shadow-sm border border-gray-100">
                <h4 class="font-medium text-gray-800 mb-3 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Remember
                </h4>
                <p class="text-gray-600 text-sm">
                  Mindfulness is a practice. Your mind will wander, and that's normal. The key is to notice when it happens and gently bring your attention back without judgment.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Self-Care Routine Builder Section -->
      <section id="self-care-routine" class="py-16 md:py-24 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16 animate-fade-in">
            <h2 class="text-3xl md:text-4xl font-sans font-bold text-dark">
              Build Your Self-Care Routine
            </h2>
            <div class="w-24 h-1 bg-secondary mx-auto mt-4 rounded-full"></div>
            <p class="mt-6 text-lg text-gray-600 max-w-3xl mx-auto">
              Create a personalized self-care routine that fits your schedule and addresses your unique needs.
            </p>
          </div>

          <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div class="p-8 md:p-10">
              <div class="grid md:grid-cols-2 gap-8">
                <!-- Left Column -->
                <div>
                  <h3 class="text-2xl font-sans font-semibold text-dark mb-6">
                    Self-Care Categories
                  </h3>

                  <div class="space-y-6">
                    <!-- Physical -->
                    <div class="bg-light rounded-xl p-6">
                      <h4 class="text-lg font-medium text-dark mb-3 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        Physical Self-Care
                      </h4>
                      <ul class="text-gray-600 space-y-2">
                        <li class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-secondary mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                          Regular exercise (even just a 10-minute walk)
                        </li>
                        <li class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-secondary mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                          Nutritious meals and proper hydration
                        </li>
                        <li class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-secondary mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                          Adequate sleep (7-9 hours)
                        </li>
                        <li class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-secondary mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                          Taking breaks from screens
                        </li>
                      </ul>
                    </div>

                    <!-- Emotional -->
                    <div class="bg-light rounded-xl p-6">
                      <h4 class="text-lg font-medium text-dark mb-3 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-accent mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        Emotional Self-Care
                      </h4>
                      <ul class="text-gray-600 space-y-2">
                        <li class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-secondary mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                          Journaling about feelings and experiences
                        </li>
                        <li class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-secondary mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                          Practicing mindfulness and meditation
                        </li>
                        <li class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-secondary mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                          Engaging in creative expression
                        </li>
                        <li class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-secondary mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                          Setting healthy boundaries
                        </li>
                      </ul>
                    </div>

                    <!-- Social -->
                    <div class="bg-light rounded-xl p-6">
                      <h4 class="text-lg font-medium text-dark mb-3 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        Social Self-Care
                      </h4>
                      <ul class="text-gray-600 space-y-2">
                        <li class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-secondary mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                          Spending time with supportive friends
                        </li>
                        <li class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-secondary mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                          Joining clubs or groups with shared interests
                        </li>
                        <li class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-secondary mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                          Volunteering or helping others
                        </li>
                        <li class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-secondary mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                          </svg>
                          Connecting with family
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>

                <!-- Right Column -->
                <div>
                  <h3 class="text-2xl font-sans font-semibold text-dark mb-6">
                    Create Your Plan
                  </h3>

                  <div class="bg-light rounded-xl p-6 mb-6">
                    <h4 class="text-lg font-medium text-dark mb-4">Self-Care Worksheet</h4>
                    <p class="text-gray-600 mb-4">
                      Download our self-care planning worksheet to create a personalized routine that works for your schedule and needs.
                    </p>
                    <a href="#" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-secondary hover:bg-secondary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary transition-smooth">
                      Download Worksheet
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                      </svg>
                    </a>
                  </div>

                  <div class="bg-light rounded-xl p-6">
                    <h4 class="text-lg font-medium text-dark mb-4">Tips for Success</h4>
                    <ul class="text-gray-600 space-y-3">
                      <li class="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                        </svg>
                        <span>Start small with 5-10 minute activities</span>
                      </li>
                      <li class="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                        </svg>
                        <span>Schedule self-care activities in your calendar</span>
                      </li>
                      <li class="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                        </svg>
                        <span>Be flexible and adjust as needed</span>
                      </li>
                      <li class="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                        </svg>
                        <span>Track what works and what doesn't</span>
                      </li>
                      <li class="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-accent mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                        </svg>
                        <span>Remember that self-care is not selfish</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Emergency Help Section -->
      <section class="py-16 md:py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="bg-gradient-to-r from-primary/10 to-accent/10 rounded-2xl p-8 md:p-10">
            <div class="flex flex-col md:flex-row items-center justify-between gap-8">
              <div class="max-w-2xl">
                <h2 class="text-2xl md:text-3xl font-sans font-bold text-dark mb-4">
                  Need Immediate Support?
                </h2>
                <p class="text-gray-700">
                  While self-care is important, sometimes you need professional help. If you're experiencing a mental health crisis or emergency, please reach out to one of these resources immediately.
                </p>
              </div>
              
              <div class="flex flex-col sm:flex-row gap-4">
                <a href="tel:+233500000000" class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-smooth hover-lift">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  Call Helpline
                </a>
                <a href="resources.html#crisis" class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-accent hover:bg-accent/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent transition-smooth hover-lift">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Crisis Resources
                </a>
              </div>
            </div>
          </div>

          <div class="mt-12 text-center">
            <p class="text-gray-500 italic">
              Remember: Self-care is not a replacement for professional mental health treatment when needed.
            </p>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid md:grid-cols-4 gap-8">
          <!-- Logo and description -->
          <div class="col-span-1 md:col-span-2">
            <a href="/" class="flex items-center">
              <span class="font-sans font-bold text-2xl text-white"
                >Say<span class="text-secondary">Something</span></span
              >
            </a>
            <p class="mt-4 text-gray-300">
              A centralized, student-friendly platform designed to raise mental
              health awareness, guide students to relevant support systems, and
              reduce stigma on university campuses.
            </p>
          </div>

          <!-- Quick links -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="index.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Home</a
                >
              </li>
              <li>
                <a
                  href="resources.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Resources</a
                >
              </li>
              <li>
                <a
                  href="awareness.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Awareness</a
                >
              </li>
              <li>
                <a
                  href="report.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Report & Support</a
                >
              </li>
              <li>
                <a
                  href="self-care.html"
                  class="text-gray-300 hover:text-white transition-smooth"
                  >Self-Care</a
                >
              </li>
            </ul>
          </div>

          <!-- Contact -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Contact</h3>
            <ul class="space-y-2">
              <li class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
                <a
                  href="mailto:<EMAIL>"
                  class="text-gray-300 hover:text-white transition-smooth"
                  ><EMAIL></a
                >
              </li>
              <li class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-secondary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <span class="text-gray-300"
                  >Takoradi Technical University, Ghana</span
                >
              </li>
            </ul>

            <div class="mt-4 flex space-x-4">
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Facebook</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                    clip-rule="evenodd"
                  />
                </svg>
              </a>
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Instagram</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fill-rule="evenodd"
                    d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                    clip-rule="evenodd"
                  />
                </svg>
              </a>
              <a
                href="#"
                class="text-gray-300 hover:text-white transition-smooth"
              >
                <span class="sr-only">Twitter</span>
                <svg
                  class="h-6 w-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                  />
                </svg>
              </a>
            </div>
          </div>
        </div>

        <div
          class="mt-12 pt-8 border-t border-gray-800 text-center text-gray-400 text-sm"
        >
          <p>Ransford Quayson | BT/ITW/24/009</p>
          <p>&copy; 2025 SaySomething. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <!-- Emergency Call Button -->
    <a href="tel:+233500000000" class="emergency-call" aria-label="Emergency call">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
      </svg>
    </a>

    <!-- JavaScript files -->
    <script src="js/main.js"></script>
  </body>
</html>
