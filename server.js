const express = require("express");
const nodemailer = require("nodemailer");
const cors = require("cors");
const path = require("path");
require("dotenv").config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static("."));

// Serve static files
app.get("/", (req, res) => {
  res.sendFile(path.join(__dirname, "index.html"));
});

// Email configuration
const transporter = nodemailer.createTransporter({
  service: "gmail",
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

// Verify email configuration
transporter.verify((error, success) => {
  if (error) {
    console.log("Email configuration error:", error);
  } else {
    console.log("Email server is ready to send messages");
  }
});

// Helper function to send emails
async function sendEmail(to, subject, html, replyTo = null) {
  const mailOptions = {
    from: process.env.EMAIL_USER,
    to: to,
    subject: subject,
    html: html,
  };

  if (replyTo) {
    mailOptions.replyTo = replyTo;
  }

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log("Email sent:", info.messageId);
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error("Email error:", error);
    return { success: false, error: error.message };
  }
}

// Contact form endpoint
app.post("/api/contact", async (req, res) => {
  try {
    const {
      "first-name": firstName,
      "last-name": lastName,
      email,
      subject,
      message,
    } = req.body;

    // Validation
    if (!firstName || !lastName || !email || !subject || !message) {
      return res.status(400).json({
        success: false,
        message: "Please fill in all required fields.",
      });
    }

    // Email to admin
    const adminEmailHtml = `
            <h2>New Contact Form Submission</h2>
            <p><strong>Name:</strong> ${firstName} ${lastName}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Subject:</strong> ${subject}</p>
            <p><strong>Message:</strong></p>
            <p>${message.replace(/\n/g, "<br>")}</p>
        `;

    // Confirmation email to user
    const userEmailHtml = `
            <h2>Thank you for reaching out to SaySomething</h2>
            <p>Hi ${firstName},</p>
            <p>We've received your message and appreciate you taking the time to contact us. Your message is important to us, and we'll review it carefully.</p>
            <p>Our team typically responds within 24-48 hours during business days. If your message is urgent, please don't hesitate to reach out to our emergency resources listed on our website.</p>
            <p>Thank you for being part of our community dedicated to mental health awareness and support.</p>
            <p>Best regards,<br>The SaySomething Team</p>
        `;

    // Send emails
    const adminResult = await sendEmail(
      process.env.EMAIL_USER,
      `Contact Form: ${subject}`,
      adminEmailHtml,
      email
    );

    const userResult = await sendEmail(
      email,
      "Thank you for contacting SaySomething",
      userEmailHtml
    );

    if (adminResult.success && userResult.success) {
      res.json({
        success: true,
        message:
          "Your message has been sent successfully. We'll get back to you soon!",
      });
    } else {
      res.status(500).json({
        success: false,
        message: "There was an issue sending your message. Please try again.",
      });
    }
  } catch (error) {
    console.error("Contact form error:", error);
    res.status(500).json({
      success: false,
      message: "An unexpected error occurred. Please try again later.",
    });
  }
});

// Awareness form endpoint (Share Your Story)
app.post("/api/awareness", async (req, res) => {
  try {
    const { name, age, program, challenges, story, consent } = req.body;

    // Validation
    if (!story || !consent) {
      return res.status(400).json({
        success: false,
        message: "Please share your story and provide consent to continue.",
      });
    }

    // Format challenges array
    const challengesList = Array.isArray(challenges)
      ? challenges.join(", ")
      : challenges || "Not specified";

    // Email to admin
    const adminEmailHtml = `
            <h2>New Story Submission</h2>
            <p><strong>Name:</strong> ${name || "Anonymous"}</p>
            <p><strong>Age:</strong> ${age || "Not provided"}</p>
            <p><strong>Program/Major:</strong> ${program || "Not provided"}</p>
            <p><strong>Challenges:</strong> ${challengesList}</p>
            <p><strong>Story:</strong></p>
            <p>${story.replace(/\n/g, "<br>")}</p>
            <p><strong>Consent given:</strong> ${consent ? "Yes" : "No"}</p>
        `;

    // Confirmation email (if name provided, we can assume they want acknowledgment)
    const userEmailHtml = `
            <h2>Thank you for sharing your story with SaySomething</h2>
            <p>Hi ${name || "there"},</p>
            <p>Thank you for courageously sharing your mental health journey with us. Your story matters and has the power to help others who may be facing similar challenges.</p>
            <p>We've received your submission and will review it carefully. If you've given consent for us to share your story, we may feature it on our website to inspire and support other students.</p>
            <p>Your willingness to speak up helps break down stigma and creates a more supportive community for everyone.</p>
            <p>If you need immediate support or resources, please visit our resources page or reach out to our recommended services.</p>
            <p>With gratitude,<br>The SaySomething Team</p>
        `;

    // Send admin email
    const adminResult = await sendEmail(
      process.env.EMAIL_USER,
      "New Story Submission - Mental Health Awareness",
      adminEmailHtml
    );

    let userResult = { success: true };
    // Only send confirmation if name is provided (indicates they want acknowledgment)
    if (name && name.trim()) {
      // We don't have their email, so we'll just log this for now
      console.log(
        "Story submission confirmation would be sent to user if email was provided"
      );
    }

    if (adminResult.success) {
      res.json({
        success: true,
        message:
          "Thank you for sharing your story. Your courage in speaking up helps others feel less alone and contributes to breaking down mental health stigma.",
      });
    } else {
      res.status(500).json({
        success: false,
        message: "There was an issue submitting your story. Please try again.",
      });
    }
  } catch (error) {
    console.error("Awareness form error:", error);
    res.status(500).json({
      success: false,
      message: "An unexpected error occurred. Please try again later.",
    });
  }
});

// Resources form endpoint (Suggest a Resource)
app.post("/api/resources", async (req, res) => {
  try {
    const {
      "resource-name": resourceName,
      "resource-type": resourceType,
      "resource-description": description,
      "resource-website": website,
      "resource-contact": contact,
      "submitter-name": submitterName,
      "submitter-email": submitterEmail,
    } = req.body;

    // Validation
    if (!resourceName || !resourceType || !description) {
      return res.status(400).json({
        success: false,
        message: "Please fill in the resource name, type, and description.",
      });
    }

    // Email to admin
    const adminEmailHtml = `
            <h2>New Resource Suggestion</h2>
            <p><strong>Resource Name:</strong> ${resourceName}</p>
            <p><strong>Type:</strong> ${resourceType}</p>
            <p><strong>Description:</strong></p>
            <p>${description.replace(/\n/g, "<br>")}</p>
            <p><strong>Website:</strong> ${website || "Not provided"}</p>
            <p><strong>Contact Info:</strong> ${contact || "Not provided"}</p>
            <hr>
            <p><strong>Submitted by:</strong> ${
              submitterName || "Anonymous"
            }</p>
            <p><strong>Submitter Email:</strong> ${
              submitterEmail || "Not provided"
            }</p>
        `;

    // Confirmation email to submitter
    const userEmailHtml = `
            <h2>Thank you for suggesting a resource</h2>
            <p>Hi ${submitterName || "there"},</p>
            <p>We've received your suggestion for "${resourceName}" and truly appreciate you helping us expand our mental health resource directory.</p>
            <p>Our team will review the resource you've suggested and consider adding it to our platform. Quality resources like the one you've shared help ensure students have access to the best possible support.</p>
            <p>Your contribution helps build a stronger, more comprehensive support network for our community.</p>
            <p>Thank you for making a difference,<br>The SaySomething Team</p>
        `;

    // Send emails
    const adminResult = await sendEmail(
      process.env.EMAIL_USER,
      `Resource Suggestion: ${resourceName}`,
      adminEmailHtml,
      submitterEmail || null
    );

    let userResult = { success: true };
    if (submitterEmail && submitterEmail.trim()) {
      userResult = await sendEmail(
        submitterEmail,
        "Thank you for your resource suggestion",
        userEmailHtml
      );
    }

    if (adminResult.success) {
      res.json({
        success: true,
        message:
          "Thank you for suggesting this resource! We'll review it and consider adding it to our directory to help other students find the support they need.",
      });
    } else {
      res.status(500).json({
        success: false,
        message:
          "There was an issue submitting your resource suggestion. Please try again.",
      });
    }
  } catch (error) {
    console.error("Resources form error:", error);
    res.status(500).json({
      success: false,
      message: "An unexpected error occurred. Please try again later.",
    });
  }
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Visit http://localhost:${PORT} to view the website`);
});
