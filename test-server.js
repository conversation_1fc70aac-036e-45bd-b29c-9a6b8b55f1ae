const express = require('express');
const nodemailer = require('nodemailer');

console.log('Testing nodemailer...');
console.log('createTransport type:', typeof nodemailer.createTransport);

const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
        user: '<EMAIL>',
        pass: 'test-password'
    }
});

console.log('Transporter created successfully!');

const app = express();
const PORT = 3000;

app.get('/', (req, res) => {
    res.send('Server is working!');
});

app.listen(PORT, () => {
    console.log(`Test server running on port ${PORT}`);
});
